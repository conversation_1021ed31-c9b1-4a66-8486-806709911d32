#!/usr/bin/env python3
"""
RAG System for Manual Grading Adjustments

This system creates embeddings for manual grading adjustments and enables semantic search
to find similar grading decisions across all marking points, not just the same marking point.
This helps improve AI grading consistency by learning from teacher feedback patterns.
"""

import os
import sys
import json
import logging
import numpy as np
from typing import List, Dict, Optional, Tuple
from datetime import datetime

# Add the parent directory to the path to import app modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from models import db, ManualGradingAdjustment, GradingAdjustmentEmbedding, MarkingPoint, Submission

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Check for required dependencies
try:
    from sentence_transformers import SentenceTransformer
    import faiss
    from sklearn.preprocessing import normalize
    DEPENDENCIES_AVAILABLE = True
except ImportError as e:
    logger.error(f"Required dependencies not installed: {e}")
    logger.error("Please install: pip install sentence-transformers faiss-cpu scikit-learn")
    DEPENDENCIES_AVAILABLE = False


class GradingRAGSystem:
    def __init__(self, model_name: str = "all-mpnet-base-v2"):
        """
        Initialize the Grading RAG system.
        
        Args:
            model_name: Sentence transformer model to use for embeddings
        """
        if not DEPENDENCIES_AVAILABLE:
            raise ImportError("Required dependencies not available")
            
        self.model_name = model_name
        self.model = None
        self.faiss_index = None
        self.adjustment_id_to_index = {}  # Map adjustment IDs to FAISS indices
        self.index_to_adjustment_id = {}  # Map FAISS indices to adjustment IDs
        
        logger.info(f"Initializing Grading RAG system with model: {model_name}")
    
    def load_model(self):
        """Load the sentence transformer model."""
        if self.model is None:
            logger.info(f"Loading sentence transformer model: {self.model_name}")
            self.model = SentenceTransformer(self.model_name)
            logger.info("Model loaded successfully")
    
    def create_context_text(self, adjustment: ManualGradingAdjustment) -> str:
        """
        Create context text for embedding from a manual grading adjustment.
        
        Args:
            adjustment: ManualGradingAdjustment object
            
        Returns:
            Combined context text for embedding
        """
        context_parts = []
        
        # Add marking point description
        if adjustment.marking_point and adjustment.marking_point.description:
            context_parts.append(f"Marking Point: {adjustment.marking_point.description}")
        
        # Add marking point score context
        if adjustment.marking_point:
            context_parts.append(f"Max Score: {adjustment.marking_point.score}")
        
        # Add student answer
        if adjustment.submission and adjustment.submission.answer:
            # Truncate very long answers to avoid embedding size issues
            answer = adjustment.submission.answer
            if len(answer) > 1000:
                answer = answer[:1000] + "..."
            context_parts.append(f"Student Answer: {answer}")
        
        # Add grading decision
        context_parts.append(f"Original Score: {adjustment.original_score}")
        context_parts.append(f"Adjusted Score: {adjustment.adjusted_score}")
        
        # Add teacher reasoning if available
        if adjustment.reason:
            context_parts.append(f"Teacher Reason: {adjustment.reason}")
        
        # Add subject/topic context if available
        if (adjustment.submission and adjustment.submission.question and 
            adjustment.submission.question.topic):
            topic = adjustment.submission.question.topic
            context_parts.append(f"Topic: {topic.name}")
            if topic.subject:
                context_parts.append(f"Subject: {topic.subject.name}")
        
        return " | ".join(context_parts)
    
    def create_embedding_for_adjustment(self, adjustment: ManualGradingAdjustment, 
                                      force_recreate: bool = False) -> bool:
        """
        Create embedding for a single manual grading adjustment.
        
        Args:
            adjustment: ManualGradingAdjustment object
            force_recreate: Whether to recreate embedding if it already exists
            
        Returns:
            True if embedding was created/updated, False otherwise
        """
        self.load_model()
        
        with app.app_context():
            # Check if embedding already exists
            existing_embedding = GradingAdjustmentEmbedding.query.filter_by(
                adjustment_id=adjustment.id,
                model_name=self.model_name
            ).first()
            
            if existing_embedding and not force_recreate:
                logger.info(f"Embedding already exists for adjustment {adjustment.id}")
                return False
            
            try:
                # Create context text
                context_text = self.create_context_text(adjustment)
                
                # Generate embedding
                embedding_vector = self.model.encode([context_text])
                embedding_vector = normalize(embedding_vector, norm='l2')[0]
                
                if existing_embedding:
                    # Update existing embedding
                    existing_embedding.embedding_vector = json.dumps(embedding_vector.tolist())
                    existing_embedding.vector_dimension = len(embedding_vector)
                    existing_embedding.context_text = context_text
                    existing_embedding.created_at = datetime.utcnow()
                    logger.info(f"Updated embedding for adjustment {adjustment.id}")
                else:
                    # Create new embedding
                    new_embedding = GradingAdjustmentEmbedding(
                        adjustment_id=adjustment.id,
                        model_name=self.model_name,
                        embedding_vector=json.dumps(embedding_vector.tolist()),
                        vector_dimension=len(embedding_vector),
                        context_text=context_text
                    )
                    db.session.add(new_embedding)
                    logger.info(f"Created new embedding for adjustment {adjustment.id}")
                
                db.session.commit()
                return True
                
            except Exception as e:
                logger.error(f"Error creating embedding for adjustment {adjustment.id}: {str(e)}")
                db.session.rollback()
                return False
    
    def create_embeddings_for_all_adjustments(self, batch_size: int = 32, 
                                            force_recreate: bool = False) -> Dict[str, int]:
        """
        Create embeddings for all manual grading adjustments.
        
        Args:
            batch_size: Number of adjustments to process in each batch
            force_recreate: Whether to recreate existing embeddings
            
        Returns:
            Dictionary with statistics about the operation
        """
        self.load_model()
        
        stats = {
            'total_adjustments': 0,
            'created_embeddings': 0,
            'updated_embeddings': 0,
            'errors': 0
        }
        
        with app.app_context():
            # Get all manual grading adjustments
            adjustments = ManualGradingAdjustment.query.all()
            stats['total_adjustments'] = len(adjustments)
            
            logger.info(f"Processing {len(adjustments)} manual grading adjustments...")
            
            for i, adjustment in enumerate(adjustments):
                try:
                    # Check if embedding exists
                    existing_embedding = GradingAdjustmentEmbedding.query.filter_by(
                        adjustment_id=adjustment.id,
                        model_name=self.model_name
                    ).first()
                    
                    if existing_embedding and not force_recreate:
                        continue
                    
                    success = self.create_embedding_for_adjustment(adjustment, force_recreate)
                    
                    if success:
                        if existing_embedding:
                            stats['updated_embeddings'] += 1
                        else:
                            stats['created_embeddings'] += 1
                    
                    # Log progress
                    if (i + 1) % 10 == 0:
                        logger.info(f"Processed {i + 1}/{len(adjustments)} adjustments")
                        
                except Exception as e:
                    logger.error(f"Error processing adjustment {adjustment.id}: {str(e)}")
                    stats['errors'] += 1
        
        logger.info(f"Embedding creation completed: {stats}")
        return stats

    def build_faiss_index(self) -> bool:
        """
        Build FAISS index from existing embeddings in the database.

        Returns:
            True if index was built successfully, False otherwise
        """
        try:
            with app.app_context():
                # Get all embeddings for this model
                embeddings = GradingAdjustmentEmbedding.query.filter_by(
                    model_name=self.model_name
                ).all()

                if not embeddings:
                    logger.warning("No embeddings found in database")
                    return False

                logger.info(f"Building FAISS index from {len(embeddings)} embeddings...")

                # Extract vectors and adjustment IDs
                vectors = []
                adjustment_ids = []

                for embedding in embeddings:
                    vector = embedding.get_vector()
                    vectors.append(vector)
                    adjustment_ids.append(embedding.adjustment_id)

                # Convert to numpy array
                vectors = np.array(vectors).astype('float32')

                # Create FAISS index
                dimension = vectors.shape[1]
                self.faiss_index = faiss.IndexFlatIP(dimension)  # Inner product for cosine similarity
                self.faiss_index.add(vectors)

                # Create mappings
                self.adjustment_id_to_index = {adj_id: i for i, adj_id in enumerate(adjustment_ids)}
                self.index_to_adjustment_id = {i: adj_id for i, adj_id in enumerate(adjustment_ids)}

                logger.info(f"FAISS index built with {len(vectors)} vectors")
                return True

        except Exception as e:
            logger.error(f"Error building FAISS index: {str(e)}")
            return False

    def search_similar_adjustments(self, query_text: str, top_k: int = 5,
                                 min_score: float = 0.3) -> List[Dict]:
        """
        Search for similar grading adjustments using semantic similarity.

        Args:
            query_text: Text to search for (marking point + student answer context)
            top_k: Number of top results to return
            min_score: Minimum similarity score threshold

        Returns:
            List of dictionaries containing adjustment data and similarity scores
        """
        if not self.faiss_index:
            logger.warning("FAISS index not built. Call build_faiss_index() first.")
            return []

        self.load_model()

        # Generate query embedding
        query_embedding = self.model.encode([query_text])
        query_embedding = normalize(query_embedding, norm='l2').astype('float32')

        # Search FAISS index
        scores, indices = self.faiss_index.search(query_embedding, top_k * 2)  # Get extra to filter

        results = []
        with app.app_context():
            for score, index in zip(scores[0], indices[0]):
                if score < min_score:
                    continue

                adjustment_id = self.index_to_adjustment_id.get(index)
                if not adjustment_id:
                    continue

                # Get adjustment from database
                adjustment = ManualGradingAdjustment.query.get(adjustment_id)
                if not adjustment:
                    continue

                result = {
                    'adjustment_id': adjustment.id,
                    'similarity_score': float(score),
                    'original_score': adjustment.original_score,
                    'adjusted_score': adjustment.adjusted_score,
                    'reason': adjustment.reason,
                    'teacher_id': adjustment.teacher_id,
                    'timestamp': adjustment.timestamp.isoformat() if adjustment.timestamp else None,
                    'marking_point_description': adjustment.marking_point.description if adjustment.marking_point else None,
                    'student_answer': adjustment.submission.answer if adjustment.submission else None,
                    'context_similarity': self._get_similarity_type(score)
                }
                results.append(result)

                if len(results) >= top_k:
                    break

        return results

    def search_similar_for_marking_point(self, marking_point_id: int, student_answer: str,
                                       top_k: int = 5, min_score: float = 0.3) -> List[Dict]:
        """
        Search for similar grading adjustments for a specific marking point and student answer.

        Args:
            marking_point_id: ID of the marking point
            student_answer: Student's answer text
            top_k: Number of top results to return
            min_score: Minimum similarity score threshold

        Returns:
            List of similar grading adjustments
        """
        with app.app_context():
            # Get marking point
            marking_point = MarkingPoint.query.get(marking_point_id)
            if not marking_point:
                logger.warning(f"Marking point {marking_point_id} not found")
                return []

            # Create query text similar to how we create context for embeddings
            query_parts = [
                f"Marking Point: {marking_point.description}",
                f"Max Score: {marking_point.score}",
                f"Student Answer: {student_answer[:1000]}"  # Truncate long answers
            ]

            # Add topic context if available
            if marking_point.part and marking_point.part.question and marking_point.part.question.topic:
                topic = marking_point.part.question.topic
                query_parts.append(f"Topic: {topic.name}")
                if topic.subject:
                    query_parts.append(f"Subject: {topic.subject.name}")

            query_text = " | ".join(query_parts)

            return self.search_similar_adjustments(query_text, top_k, min_score)

    def _get_similarity_type(self, score: float) -> str:
        """Get similarity type based on score."""
        if score >= 0.8:
            return "very_high"
        elif score >= 0.6:
            return "high"
        elif score >= 0.4:
            return "medium"
        else:
            return "low"


def create_embedding_for_new_adjustment(adjustment_id: int) -> bool:
    """
    Utility function to create embedding for a newly created manual grading adjustment.
    This can be called from the API when a new adjustment is saved.

    Args:
        adjustment_id: ID of the manual grading adjustment

    Returns:
        True if embedding was created successfully, False otherwise
    """
    try:
        rag_system = GradingRAGSystem()

        with app.app_context():
            adjustment = ManualGradingAdjustment.query.get(adjustment_id)
            if not adjustment:
                logger.error(f"Adjustment {adjustment_id} not found")
                return False

            return rag_system.create_embedding_for_adjustment(adjustment)

    except Exception as e:
        logger.error(f"Error creating embedding for adjustment {adjustment_id}: {str(e)}")
        return False


def get_rag_context_for_grading(marking_point_id: int, student_answer: str,
                               top_k: int = 3, min_score: float = 0.4) -> str:
    """
    Get RAG-based context for grading a student answer.
    This replaces the old _get_manual_adjustment_context function.

    Args:
        marking_point_id: ID of the marking point
        student_answer: Student's answer text
        top_k: Number of similar adjustments to include
        min_score: Minimum similarity score threshold

    Returns:
        Formatted context string for the AI grading prompt
    """
    try:
        rag_system = GradingRAGSystem()

        # Build FAISS index if not already built
        if not rag_system.faiss_index:
            if not rag_system.build_faiss_index():
                logger.warning("Could not build FAISS index for grading context")
                return ""

        # Search for similar adjustments
        similar_adjustments = rag_system.search_similar_for_marking_point(
            marking_point_id, student_answer, top_k, min_score
        )

        if not similar_adjustments:
            logger.info("No similar grading adjustments found")
            return ""

        # Format context for AI prompt
        context_lines = [
            "TEACHER GRADING EXAMPLES: The following are similar grading decisions made by teachers. "
            "Use these as strong guidance for grading consistency. If the student's answer is very "
            "similar to any of these examples, follow the teacher's grading decision closely."
        ]

        for i, adj in enumerate(similar_adjustments, 1):
            context_lines.append(f"\nExample {i} (similarity: {adj['similarity_score']:.2f}):")
            context_lines.append(f"  Marking Point: {adj['marking_point_description']}")
            context_lines.append(f"  Student Answer: \"{adj['student_answer'][:200]}{'...' if len(adj['student_answer']) > 200 else ''}\"")
            context_lines.append(f"  Teacher's Score: {adj['adjusted_score']}")
            if adj['reason']:
                context_lines.append(f"  Teacher's Reasoning: {adj['reason']}")

        context_lines.append("\nUse these examples to maintain grading consistency.")

        return "\n".join(context_lines)

    except Exception as e:
        logger.error(f"Error getting RAG context for marking point {marking_point_id}: {str(e)}")
        return ""


def main():
    """Main function for testing and setup."""
    if not DEPENDENCIES_AVAILABLE:
        print("❌ Required dependencies not available")
        print("Please install: pip install sentence-transformers faiss-cpu scikit-learn")
        return

    print("🚀 Grading RAG System Setup")
    print("=" * 50)

    # Initialize Flask app context
    with app.app_context():
        # Initialize RAG system
        rag_system = GradingRAGSystem()

        # Create embeddings for all existing adjustments
        print("📝 Creating embeddings for manual grading adjustments...")
        stats = rag_system.create_embeddings_for_all_adjustments()

        print(f"📊 Embedding Statistics:")
        print(f"  Total adjustments: {stats['total_adjustments']}")
        print(f"  Created embeddings: {stats['created_embeddings']}")
        print(f"  Updated embeddings: {stats['updated_embeddings']}")
        print(f"  Errors: {stats['errors']}")

        # Build FAISS index
        print("\n🔍 Building FAISS index...")
        if rag_system.build_faiss_index():
            print("✅ FAISS index built successfully!")

            # Test the system
            print("\n🧪 Testing RAG system...")
            test_query = "organic chemistry functional groups student answer"
            results = rag_system.search_similar_adjustments(test_query, top_k=3)

            print(f"Found {len(results)} similar adjustments for test query:")
            for i, result in enumerate(results, 1):
                print(f"  {i}. Score: {result['adjusted_score']} (similarity: {result['similarity_score']:.3f})")
        else:
            print("❌ Failed to build FAISS index")


if __name__ == "__main__":
    main()
