from flask import request, jsonify, session, flash, redirect, url_for, current_app, render_template # Added current_app and render_template
from datetime import datetime, timedelta
import google.generativeai as genai
from pinecone import Pinecone
import os
import re
import json
import base64
import urllib.parse
import time
import markdown
from bs4 import BeautifulSoup
from concurrent.futures import ThreadPoolExecutor, as_completed
from models import db, Part, Submission, IncompleteSubmission, ProblemSetSubmission, MarkingPoint, DailyActivity, DailyActiveTime, User, Question, NotesChunk, NotesEmbedding, ManualGradingAdjustment, GradingAdjustmentEmbedding
# Import utilities and decorators
from .utils import login_required, update_user_activity, error_logger, app_logger, admin_required

# Note: AI clients (Groq, Mistral) might be better initialized in the app factory
# and passed here, or accessed via app.config/current_app.
# For now, assume they are passed to the registration function.

class GradingTimer:
    """Utility class to track timing for grading steps"""
    def __init__(self):
        self.steps = []
        self.start_time = time.time()
        self.current_step_start = None

    def start_step(self, step_name):
        """Start timing a new step"""
        current_time = time.time()
        if self.current_step_start is not None:
            # End the previous step
            self.end_current_step()
        self.current_step_start = current_time
        self.current_step_name = step_name

    def end_current_step(self):
        """End the current step and record its duration"""
        if self.current_step_start is not None:
            duration = time.time() - self.current_step_start
            self.steps.append({
                'name': self.current_step_name,
                'duration_ms': round(duration * 1000, 2),
                'duration_s': round(duration, 3)
            })
            self.current_step_start = None

    def get_summary(self):
        """Get a summary of all timing steps"""
        # End current step if still running
        if self.current_step_start is not None:
            self.end_current_step()

        total_duration = time.time() - self.start_time
        return {
            'total_duration_ms': round(total_duration * 1000, 2),
            'total_duration_s': round(total_duration, 3),
            'steps': self.steps,
            'step_count': len(self.steps)
        }

def _get_relevant_notes_sections(question_text: str, max_sections: int = 3):
    """
    Get relevant notes sections for a question using the RAG system.

    Args:
        question_text: The question text to search for
        max_sections: Maximum number of sections to return (default: 3)

    Returns:
        List of relevant notes sections with metadata
    """
    try:
        # Check if RAG dependencies are available
        try:
            from notes_rag_system import NotesRAGSystem, DEPENDENCIES_AVAILABLE
            if not DEPENDENCIES_AVAILABLE:
                return []
        except ImportError:
            return []

        # Initialize RAG system
        rag_system = NotesRAGSystem()

        # Search for relevant chunks
        results = rag_system.search_similar_chunks(
            question_text,
            top_k=max_sections,
            min_score=0.4  # Higher threshold for question relevance
        )

        # Format results for frontend
        relevant_sections = []
        for result in results:
            chunk = result['chunk']
            section = {
                'title': chunk['title'],
                'content': chunk['content'][:300] + '...' if len(chunk['content']) > 300 else chunk['content'],
                'filename': chunk['filename'],
                'chapter_id': chunk['chapter_id'],
                'section_id': chunk['section_id'],
                'similarity_score': result['similarity_score'],
                'relevance_type': result['relevance_type'],
                'url': f"/notes/{chunk['chapter_id']}#{chunk['section_id']}"
            }
            relevant_sections.append(section)

        return relevant_sections

    except Exception as e:
        # Log error but don't fail the grading process
        print(f"Warning: Error getting relevant notes sections: {e}")
        return []

# Multi-color highlighting helper functions (moved to module level for testing)
def convert_border_to_highlight_class(border_class):
    """
    Convert border class (e.g., 'border-yellow-400') to highlight class (e.g., 'highlight-yellow')
    """
    color_mapping = {
        'border-yellow-400': 'highlight-yellow',
        'border-blue-400': 'highlight-blue',
        'border-green-400': 'highlight-green',
        'border-pink-400': 'highlight-pink',
        'border-purple-400': 'highlight-purple',
        'border-indigo-400': 'highlight-indigo',
        'border-teal-400': 'highlight-teal',
        'border-orange-400': 'highlight-orange',
        'border-lime-400': 'highlight-lime',
        'border-cyan-400': 'highlight-cyan'
    }
    return color_mapping.get(border_class, 'highlight-yellow')  # Default fallback

def generate_multi_color_class(color_classes):
    """
    Generate appropriate CSS class for multiple overlapping colors
    """
    # Separate partial and regular classes
    base_colors = []
    has_partial = False
    clean_classes = []

    for color_class in color_classes:
        if 'highlight-partial' in color_class:
            has_partial = True
            # Remove the partial class to get the base color
            clean_class = color_class.replace(' highlight-partial', '')
            clean_classes.append(clean_class)
        else:
            clean_classes.append(color_class)

        # Extract base color names from clean classes
        clean_class = color_class.replace(' highlight-partial', '')
        if 'highlight-' in clean_class and 'highlight-base' not in clean_class:
            base_color = clean_class.replace('highlight-', '')
            if base_color not in base_colors:
                base_colors.append(base_color)

    num_colors = len(base_colors)

    # Build the base class string
    if num_colors == 1:
        result_class = clean_classes[0].replace(' highlight-partial', '') + ' highlight-base'
    elif num_colors == 2:
        # For two colors, use the specific two-color combination class
        sorted_colors = sorted(base_colors)
        result_class = f"highlight-{sorted_colors[0]} highlight-{sorted_colors[1]} highlight-base"
    elif num_colors == 3:
        # For three colors, use the multi-3 class
        result_class = 'highlight-multi-3 highlight-base'
    else:
        # For 4+ colors, use the multi-4plus class
        result_class = 'highlight-multi-4plus highlight-base'

    # Add partial class if any of the overlapping highlights are partial
    if has_partial:
        result_class += ' highlight-partial'

    return result_class

def create_highlighted_answer_with_code(evidence_list, user_answer):
    print("DATA:",evidence_list, user_answer)
    """
    Create highlighted answer using code-based span wrapping.
    Matches evidence_snippets to user_answer and creates HTML spans with proper colors.
    Handles overlapping evidence by combining CSS classes with new multi-color system.
    """
    import html
    import re

    if not evidence_list or not user_answer:
        return html.escape(user_answer)

    # Collect all character ranges that need highlighting with their colors
    highlight_ranges = []

    for evidence_item in evidence_list:
        evidence_snippets = evidence_item.get('evidence_snippets', [])
        color_class = evidence_item.get('color_class', '')
        achieved = evidence_item.get('achieved', False)
        partial = evidence_item.get('partial', False)

        if not evidence_snippets or not color_class:
            continue

        # Convert border class to highlight class
        highlight_class = convert_border_to_highlight_class(color_class)

        # Add partial class if only partially achieved
        if partial and not achieved:
            full_color_class = highlight_class + ' highlight-partial'
        else:
            full_color_class = highlight_class

        # Find character indices for each evidence snippet dynamically
        for snippet in evidence_snippets:
            if not snippet:
                continue

            # Clean up evidence text (remove extra whitespace and normalize)
            evidence_clean = re.sub(r'\s+', ' ', snippet.strip())

            # First try exact match (case-insensitive)
            user_answer_lower = user_answer.lower()
            evidence_lower = evidence_clean.lower()

            start_pos = 0
            snippet_found = False
            while True:
                pos = user_answer_lower.find(evidence_lower, start_pos)
                if pos == -1:
                    break

                start_idx = pos
                end_idx = pos + len(evidence_clean)

                if start_idx >= 0 and end_idx <= len(user_answer) and start_idx < end_idx:
                    highlight_ranges.append({
                        'start': start_idx,
                        'end': end_idx,
                        'color_class': full_color_class,
                        'snippet': evidence_clean
                    })
                    snippet_found = True

                start_pos = pos + 1

            # If no exact match found, try space-agnostic matching
            if not snippet_found:
                # Remove ALL spaces for comparison
                def remove_all_spaces(text):
                    return re.sub(r'\s', '', text)

                snippet_no_spaces = remove_all_spaces(evidence_clean).lower()
                user_answer_no_spaces = remove_all_spaces(user_answer).lower()

                # Find the position of the snippet in the space-stripped user answer
                if snippet_no_spaces in user_answer_no_spaces:
                    start_pos_no_spaces = user_answer_no_spaces.find(snippet_no_spaces)
                    end_pos_no_spaces = start_pos_no_spaces + len(snippet_no_spaces)

                    # Map back to original positions with spaces
                    original_start = 0
                    original_end = 0
                    char_count_no_spaces = 0

                    # Find the start position in original text
                    for i, char in enumerate(user_answer):
                        if not char.isspace():
                            if char_count_no_spaces == start_pos_no_spaces:
                                original_start = i
                                break
                            char_count_no_spaces += 1

                    # Find the end position in original text
                    char_count_no_spaces = 0
                    for i, char in enumerate(user_answer):
                        if not char.isspace():
                            char_count_no_spaces += 1
                            if char_count_no_spaces == end_pos_no_spaces:
                                original_end = i + 1
                                break

                    # Add the match with original spacing preserved
                    if original_start < original_end:
                        highlight_ranges.append({
                            'start': original_start,
                            'end': original_end,
                            'color_class': full_color_class,
                            'snippet': user_answer[original_start:original_end]  # Use original text with spaces
                        })
                        snippet_found = True

            # If still no match found, try fuzzy matching for key terms
            print("finding:",snippet,":",snippet_found)

    if not highlight_ranges:
        return html.escape(user_answer)

    # Sort ranges by start position
    highlight_ranges.sort(key=lambda x: x['start'])

    # Create a character-by-character mapping of colors
    char_colors = {}  # char_index -> set of color classes
    char_marking_points = {}  # char_index -> set of marking point info

    for range_info in highlight_ranges:
        for i in range(range_info['start'], range_info['end']):
            if i not in char_colors:
                char_colors[i] = set()
                char_marking_points[i] = set()
            char_colors[i].add(range_info['color_class'])
            char_marking_points[i].add(range_info['snippet'])

    # Build the highlighted HTML
    result_parts = []
    i = 0

    while i < len(user_answer):
        if i in char_colors:
            # Start of highlighted section
            current_colors = char_colors[i]
            current_snippets = char_marking_points[i]

            # Find the end of this color combination
            j = i
            while j < len(user_answer) and j in char_colors and char_colors[j] == current_colors:
                j += 1

            # Generate appropriate multi-color class
            color_list = list(current_colors)
            combined_class = generate_multi_color_class(color_list)

            # Extract and escape the text
            text_segment = user_answer[i:j]
            escaped_text = html.escape(text_segment)

            # Create enhanced title for tooltip
            num_points = len(current_colors)
            if num_points == 1:
                title_text = f"Evidence found for 1 marking point"
            else:
                title_text = f"Evidence found for {num_points} marking points"

            # Add snippet information to tooltip
            snippet_info = " | ".join(list(current_snippets)[:3])  # Show up to 3 snippets
            if len(current_snippets) > 3:
                snippet_info += f" (and {len(current_snippets) - 3} more)"

            full_title = f"{title_text}: {snippet_info}"

            # Add data attributes for multi-color styling
            data_attrs = ""
            if num_points >= 3:
                data_attrs = f' data-count="{num_points}"'
                if num_points == 3:
                    # For 3-color combinations, we could add color data
                    data_attrs += f' data-colors="{num_points} colors"'

            # Add the span
            result_parts.append(f'<span class="{combined_class}" title="{html.escape(full_title)}"{data_attrs}>{escaped_text}</span>')

            i = j
        else:
            # Regular character, escape and add
            result_parts.append(html.escape(user_answer[i]))
            i += 1

    return ''.join(result_parts)

def register_api_routes(app, db, session, limiter, groq_client, mistral_client, gemini_grading_client): # Pass limiter and AI clients

    def is_kimi_k2_available():
        """Check if Kimi K2 is available through Groq client"""
        return groq_client is not None

    def generate_missed_feedback_with_bold(marking_point_description, missed_snippets):
        """
        Generate feedback with bolded missed parts for partial marking points.

        Args:
            marking_point_description (str): The original marking point description
            missed_snippets (list): List of text snippets that the user missed

        Returns:
            str: The marking point description with missed parts bolded
        """
        if not missed_snippets:
            return marking_point_description

        # Create a copy of the description to modify
        bolded_description = marking_point_description

        # Bold each missed snippet in the description
        for snippet in missed_snippets:
            if not snippet or not snippet.strip():
                continue

            snippet_clean = snippet.strip()

            # First try exact match
            if snippet_clean in bolded_description:
                bolded_description = bolded_description.replace(
                    snippet_clean,
                    f"<strong>{snippet_clean}</strong>"
                )
            else:
                # Try space-agnostic matching
                import re

                # Remove ALL spaces for comparison
                def remove_all_spaces(text):
                    return re.sub(r'\s', '', text)

                snippet_no_spaces = remove_all_spaces(snippet_clean)
                description_no_spaces = remove_all_spaces(bolded_description)

                # Find the position of the snippet in the space-stripped description
                if snippet_no_spaces in description_no_spaces:
                    start_pos = description_no_spaces.find(snippet_no_spaces)
                    end_pos = start_pos + len(snippet_no_spaces)

                    # Now we need to map back to the original description with spaces
                    # Count characters (including spaces) to find the real positions
                    original_start = 0
                    original_end = 0
                    char_count_no_spaces = 0

                    # Find the start position in original text
                    for i, char in enumerate(bolded_description):
                        if not char.isspace():
                            if char_count_no_spaces == start_pos:
                                original_start = i
                                break
                            char_count_no_spaces += 1

                    # Find the end position in original text
                    char_count_no_spaces = 0
                    for i, char in enumerate(bolded_description):
                        if not char.isspace():
                            char_count_no_spaces += 1
                            if char_count_no_spaces == end_pos:
                                original_end = i + 1
                                break

                    # Extract the original text with spaces and replace it
                    if original_start < original_end:
                        original_text = bolded_description[original_start:original_end]
                        bolded_text = f"<strong>{original_text}</strong>"
                        bolded_description = bolded_description[:original_start] + bolded_text + bolded_description[original_end:]

        return bolded_description
    def _process_single_marking_point(mp_data, mp_index, user_answer, part_data, marking_points_data,
                                     app_logger, assigned_border_class):
        """
        Process a single marking point and return the evaluation result.
        This function is designed to be called in parallel for each marking point.
        """
        point_score = 0
        is_correct_mp = False
        is_partial_mp = False
        evidence_mp = None
        # Note: We no longer use evidence_indices, only evidence_snippets
        error_mp = False

        try:
            # Get common mistakes for this question/part if they exist
            from misconception_utils import get_part_misconceptions
            common_mistakes = get_part_misconceptions(part_data.question_id, part_data.id)

            # Build common mistakes section if mistakes exist
            common_mistakes_section = ""
            if common_mistakes:
                mistakes_list = "\n".join([f"- {mistake}" for mistake in common_mistakes])
                common_mistakes_section = f"""
COMMON STUDENT MISTAKES TO WATCH FOR:
The following are typical misconceptions and errors students make for this question part. Do NOT award points if the student's answer demonstrates these mistakes:
{mistakes_list}

"""

            # Generate LLM prompt
            prompt = f"""\
                You are an expert examiner evaluating a student's answer against a specific marking point.
                TASK:
                1. Determine if the student's answer demonstrates understanding of the marking point
                2. Classify the answer and provide structured feedback in one of these formats:
                   - If FULLY CORRECT: "Correctly identified <concept>"
                   - If the student attempted to address the concept but was INCORRECT: "Incorrecty explained <concept"
                   - If the student OMITTED the concept entirely: "Omitted <concept>"
                3. For FULLY or PARTIALLY correct answers, identify ALL exact text (if the input used a special character, use that same special character back instead of using plain text) snippets from the student's answer that provide evidence. (Take special note of whitespace around special characters)
                4. If there are multiple evidence snippets, separate them with " | " (space-pipe-space)
                5. I am using the same prompt for all marking points, and so if you think that a part of the answer is MORE relevant to the other point instead of this marking point, you MUST not include it in the EVIDENCE section because it will be counted as evidence for the other marking point. THIS IS IMPORTANT! Think about which marking point the text is most relevant to and include it in the EVIDENCE section ONLY for that marking point.

                {common_mistakes_section}RESPONSE FORMAT:
                You must respond in one of these 3 formats ONLY:

                Format 1 - If the marking point is FULLY addressed:
                YES
                EVIDENCE: <exact text snippet 1> | <exact text snippet 2> | <exact text snippet 3>

                Format 2 - If the marking point was PARTIALLY addressed:
                PARTIAL
                EVIDENCE: <exact text snippet 1> | <exact text snippet 2> | <exact text snippet 3>

                Format 3 - If the marking point is NOT addressed:
                NO

                IMPORTANT:
                - Do not include any other text, explanations, or formatting in your response
                - For evidence, provide the EXACT text as it appears in the student's answer
                - If multiple snippets, separate with " | " (space-pipe-space)
                - Each snippet should be a meaningful phrase or sentence, not just single words

                MARKING POINT: {mp_data['description']}
                OTHER MARKING POINTS (exclude these from your evaluation): {', '.join([other_mp['description'] for other_mp in marking_points_data if other_mp['id'] != mp_data['id']])}
                STUDENT'S ANSWER: {user_answer}
                """

            # Call LLM
            generation_config = {
                "temperature": 0.1, 
            }
            safety_settings = [
                {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_NONE"},
                {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_NONE"},
                {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_NONE"},
                {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_NONE"}
            ]
            response_text = gemini_grading_client.generate_content(
                prompt,
                generation_config=generation_config,
                safety_settings=safety_settings
            ).text.strip()

            # Process response
            response_upper = response_text.upper()
            is_correct_mp = 'YES' in response_upper and not 'PARTIAL' in response_upper
            is_partial_mp = 'PARTIAL' in response_upper

            app_logger.debug(f"LLM Response for marking point {mp_data['id']}: {response_text}")

            # Extract evidence for correct/partial answers and find character indices
            evidence_snippets = []  # List to store multiple evidence snippets
            if is_correct_mp or is_partial_mp:
                if 'EVIDENCE:' in response_text:
                    evidence_parts = response_text.split('EVIDENCE:', 1)
                    if len(evidence_parts) > 1:
                        evidence_mp = evidence_parts[1].strip()
                elif 'EVIDENCE' in response_text: # Fallback if colon is missing
                    evidence_parts = response_text.split('EVIDENCE', 1)
                    if len(evidence_parts) > 1:
                        evidence_mp = evidence_parts[1].lstrip(':').strip()

                # Split evidence by " | " to get multiple snippets
                if evidence_mp:
                    raw_snippets = [snippet.strip() for snippet in evidence_mp.split(' | ')]
                    evidence_snippets = [snippet for snippet in raw_snippets if snippet]

                # Note: Character indices will be calculated dynamically from evidence_snippets during highlighting

            # Calculate score
            if is_correct_mp:
                point_score = mp_data['score']
            elif is_partial_mp:
                point_score = mp_data['score'] * 0.5

        except Exception as e_mp:
            app_logger.exception(f"Error evaluating marking point {mp_data['id']} with LLM: {str(e_mp)}")
            error_mp = True
        # Return result
        return {
            'id': mp_data['id'],
            'description': mp_data['description'],
            'score': mp_data['score'],
            'achieved': is_correct_mp,
            'partial': is_partial_mp,
            'achieved_score': point_score,
            'evidence': evidence_mp if 'evidence_mp' in locals() else None,
            'evidence_snippets': evidence_snippets,  # List of evidence snippets for highlighting
            'missed_snippets': [],  # Empty for Gemini version (MISSED functionality only in Kimi K2)
            'feedback': mp_data['description'],  # Return the marking point description as the correct answer
            'color': assigned_border_class if (is_correct_mp or is_partial_mp) and evidence_snippets else None,
            'error': error_mp,
            'mp_index': mp_index  # Include index for ordering results
        }

    def _get_manual_adjustment_context(marking_point_id, user_answer, app_logger):
        """
        Get context from similar manual adjustments using RAG-based semantic search.
        This finds similar grading decisions across all marking points, not just the same one.
        Returns a string with relevant context or empty string if no context available.
        """
        try:
            app_logger.info(f"Getting RAG-based manual adjustment context for marking_point_id={marking_point_id}")

            # Import here to avoid circular imports
            from grading_rag_system import get_rag_context_for_grading

            # Get RAG-based context
            context = get_rag_context_for_grading(
                marking_point_id=marking_point_id,
                student_answer=user_answer,
                top_k=3,  # Get top 3 most similar adjustments
                min_score=0.4  # Minimum similarity threshold
            )

            if context:
                app_logger.info(f"Found RAG context with {len(context.split('Example'))-1} similar adjustments")
                print("RAG context:", context[:500] + "..." if len(context) > 500 else context)
            else:
                app_logger.info("No similar adjustments found via RAG")

                # Fallback to old method for same marking point only
                app_logger.info("Falling back to same-marking-point lookup")
                recent_adjustments = ManualGradingAdjustment.query.filter(
                    ManualGradingAdjustment.marking_point_id == marking_point_id
                ).order_by(ManualGradingAdjustment.timestamp.desc()).limit(3).all()

                if recent_adjustments:
                    context_lines = ["Sample grading results for this specific marking point:"]
                    for adj in recent_adjustments:
                        submission = adj.submission
                        context_lines.append(f"Score: {adj.adjusted_score}")
                        if adj.reason:
                            context_lines.append(f"  Reason: {adj.reason}")
                        context_lines.append(f"  Student answer: \"{submission.answer}\"")
                        context_lines.append("")
                    context = "\n".join(context_lines)
                    app_logger.info(f"Found {len(recent_adjustments)} adjustments for same marking point")

            return context

        except Exception as e:
            app_logger.warning(f"Error getting manual adjustment context for marking point {marking_point_id}: {str(e)}")
            return ""

    def _process_single_marking_point_kimi(mp_data, mp_index, user_answer, part_data, marking_points_data,
                                          app_logger, assigned_border_class, manual_adjustment_context=""):
        """
        Process a single marking point using Kimi K2 and return the evaluation result.
        This function is designed to be called in parallel for each marking point.
        """
        point_score = 0
        is_correct_mp = False
        is_partial_mp = False
        evidence_mp = None
        error_mp = False
        detected_mistakes = []

        try:
            # Get common mistakes for this question/part if they exist
            from misconception_utils import get_part_misconceptions
            common_mistakes = get_part_misconceptions(part_data.question_id, part_data.id)

            # Build common mistakes section if mistakes exist
            common_mistakes_section = ""
            if common_mistakes:
                mistakes_list = "\n".join([f"- {mistake}" for mistake in common_mistakes])
                common_mistakes_section = f"""
COMMON STUDENT MISTAKES TO WATCH FOR:
The following are typical misconceptions and errors students make for this question part. Do NOT award points if the student's answer demonstrates these mistakes:
{mistakes_list}

"""

            # Build additional context from comments
            additional_context = ""
            if part_data.question.comments:
                additional_context += f"\nQUESTION CONTEXT: {part_data.question.comments}"
            if part_data.comments:
                additional_context += f"\nPART CONTEXT: {part_data.comments}"

            # Use manual adjustment context passed as parameter
            if manual_adjustment_context:
                additional_context += f"\n{manual_adjustment_context}"
                print("Using manual adjustment context:", manual_adjustment_context[:200] + "..." if len(manual_adjustment_context) > 200 else manual_adjustment_context)
            # Load grading examples from file
            try:
                with open('gradingexamples.txt', 'r', encoding='utf-8') as f:
                    grading_examples = f.read().strip()
            except FileNotFoundError:
                app_logger.warning("gradingexamples.txt not found, using fallback examples")
                grading_examples = """Examples:
- If the student missed out on key technical terms for chemistry especially, like writing "electron" instead of "p-orbital"s, you must NOT mark it as correct.
- If the student uses a different way of expression, such as using Na instead of Sodium, it is seen as correct and you do not need to penalize.
- If the student compares the other way, eg: uses "A is less than B"instead of "B is greater than A", it is seen as correct and you do not need to penalize.
- If the student is more specific than required, but it is still correct, you do not need to penalize. eg: If the student writes "smaller ionic radius" instead of "smaller radius" => higher charge density, it is still correct.
- If the student is less specific than required, but is still correct, you should at least give PARTIAL. eg: If the student writes "... is polar and hence has stronger pd-pd bonds" instead of "... is polar and hence has stronger H bonds" you should at least give PARTIAL credit."""

            # Generate LLM prompt for marking point evaluation
            prompt = f"""\
                TASK:

                1. Determine if the student's answer would achieve this marking point (If the student's answer has something that means the same thing as this marking point). Please only give a YES if the marking point is explicitly written in the answer. (no deductions needed, just plainly if the marking point means the same thing as a part of the answer).
                If the student has the relevant ideas and is close to getting fully correct, give PARTIAL. PARTIAL is only for the case where you are unsure if it is a NO or a YES, and if there are arguments for both verdicts. If the student's statement is factually incorrect / a misconception / changes the meaning , you should mark it as NO or PARTIAL, even if the it only differs by one or two words.
                {grading_examples}
                Paraphrases that changes the meaning or slight omissions should be marked as NO/PARTIAL. We must be strict. However, if the student paraphrases the marking point without changing the meaning, you must NOT penalize. (Give YES). If the student paraphrases the marking point without changing the meaning, you must NOT penalize. (Give YES).
                For example, paraphrasing "same quantum number" to "in the same quantum shell" should not be penalized.
                2. You should only give YES if the student's answer is correct and is explicitly shown in the answer.
                3. For FULLY or PARTIALLY correct answers, identify ALL exact (same case, same spacing) text snippets from the student's answer that provide evidence. do NOT write extra formatting things like ** around the text.
                4. As long as the student got the main idea correct in a marking point, you should give them FULLY CORRECT. IF YOU JUDGE THE ANSWER OMITTED **KEY** PHRASES, mark it as partially correct. For PARTIALLY correct answers, you MUST be able to identify exact texts FROM THE MARKING POINT that the user omitted from their answer. Please keep the MISSED sections moderately short and succint, (only bolding KEY PHRASES and NOT whole sentences), only extract the MOST key ideas that the user omitted.
                6. If there are multiple evidence snippets, separate them with " | "
                7. I am using the same prompt for all marking points, and so if you think that a part of the answer is MORE relevant to the other point instead of this marking point, you MUST not include it in the EVIDENCE section because it will be counted as evidence for the other marking point.
                8. Avoid extracting too long of an evidence for a single marking point. If the student's answer is long, try to extract the most relevant part that directly has the same meaning as the marking point.
                9. If the original text uses special character, you must use special characters as well in your extracted text for both evidence and missed, instead of writing plain text.
                10. Format: separate text snippets with | only. do not use ... or other separators

                {common_mistakes_section}
                Additional comments to grade for this question: PLEASE PRIORITISE THIS: {additional_context}

                RESPONSE FORMAT:
                You must respond in one of these 3 formats ONLY:

                Format 1 - If the marking point is FULLY addressed:
                YES
                EVIDENCE: <exact text snippet 1> | <exact text snippet 2>
                repeat this structure for as many text snippets as required.

                Format 2 - If the marking point was PARTIALLY addressed: (YOU MUST HAVE AT LEAST 1 TEXT SNIPPET FOR MISSED. ELSE, MARK IT AS YES. do NOT wrap the text in **)
                PARTIAL
                EVIDENCE: <exact text snippet 1> | <exact text snippet 2>
                MISSED: <exact text snippet 1> | <exact text snippet 2>
                repeat this structure for as many text snippets as required.

                Format 3 - If the marking point is NOT addressed:
                NO

                IMPORTANT:
                - Do not include any other text, explanations, or formatting in your response
                - For evidence, provide the EXACT text as it appears in the student's answer
                - If multiple snippets, separate with " | " (space-pipe-space)
                - Each snippet should be a meaningful phrase or sentence, not just single words

                MARKING POINT: {mp_data['description']}
                OTHER MARKING POINTS, separated by ; (exclude these from your evaluation): {';'.join([other_mp['description'] for other_mp in marking_points_data if other_mp['id'] != mp_data['id']])}
                STUDENT'S ANSWER: {user_answer}
                """
            print("PROMPT:",prompt)

            # Call Kimi K2 API through Groq
            completion = groq_client.chat.completions.create(
                model="moonshotai/kimi-k2-instruct",
                messages=[
                    {"role": "system", "content": "You are a very strict examiner evaluating a student's answer against a specific marking point for high school level chemistry questions. Your job is to be as strict as possible, catching any deviations from the marking point so the student can learn from their mistakes. You are very strict and you must penalize the user for using the wrong technical terms, as it will be the focal point of marking. For example, if the student writes electron overlaps with ... instead of p-orbital overlaps with ..., even if the rest of the answer is correct, you must mark it as NO. Do not penalize for english mistakes like grammar, spelling, punctuation, etc."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.03,
                seed=42
            )

            response_text = completion.choices[0].message.content.strip()
            # delete everything until </think>
            # response_text = response_text.split('</think>')[1]
            app_logger.debug(f"Kimi K2 Response for marking point {mp_data['id']}: {response_text}")

            # Extract evidence and missed content from response
            evidence_snippets = []  # List to store multiple evidence snippets
            missed_snippets = []  # List to store missed content for partial answers

            # Process EVIDENCE line (first line)
            if 'EVIDENCE:' in response_text:
                evidence_parts = response_text.split('EVIDENCE:', 1)
                if len(evidence_parts) > 1:
                    evidence_section = evidence_parts[1].strip()

                    # Check if there's a MISSED section after EVIDENCE
                    if 'MISSED:' in evidence_section:
                        evidence_and_missed = evidence_section.split('MISSED:', 1)
                        evidence_only = evidence_and_missed[0].strip()
                        missed_only = evidence_and_missed[1].strip()

                        # Process evidence snippets
                        if evidence_only:
                            raw_snippets = [snippet.strip() for snippet in evidence_only.split(' | ')]
                            evidence_snippets = [snippet for snippet in raw_snippets if snippet]

                        # Process missed snippets
                        if missed_only:
                            raw_missed = [snippet.strip() for snippet in missed_only.split(' | ')]
                            missed_snippets = [snippet for snippet in raw_missed if snippet]
                    else:
                        # No MISSED section, just process evidence
                        if evidence_section:
                            raw_snippets = [snippet.strip() for snippet in evidence_section.split(' | ')]
                            evidence_snippets = [snippet for snippet in raw_snippets if snippet]

            # Generate status based on evidence and missed content
            # if missed is blank: YES
            # If missed + evidence exist: PARTIALLY CORRECT
            # If evidence is blank: NO
            if not missed_snippets and evidence_snippets:
                is_correct_mp = True
                is_partial_mp = False
            elif missed_snippets and evidence_snippets:
                is_correct_mp = False
                is_partial_mp = True
            else:  # No evidence snippets (regardless of missed)
                is_correct_mp = False
                is_partial_mp = False
            # Calculate score
            if is_correct_mp:
                point_score = mp_data['score']
            elif is_partial_mp:
                point_score = mp_data['score'] * 0.5

            # Check for common mistakes if we have them
            if common_mistakes:
                print(f"🔍 CHECKING FOR MISTAKES - Question {part_data.question_id}, Part {part_data.id}, MP {mp_data['id']}: {len(common_mistakes)} mistakes to check")
                mistake_detection_prompt = f"""\
                TASK: Analyze if the student's answer demonstrates any of the common mistakes listed below.

                common mistake suggestions (These may be wrong, but may not be wrong, please exercise judgement if it's actually wrong):
                {chr(10).join([f"{i+1}. {mistake}" for i, mistake in enumerate(common_mistakes)])}

                RESPONSE FORMAT:
                For each mistake that is present in the student's answer, respond with:
                MISTAKE_DETECTED: [mistake number]

                If no mistakes are detected, respond with:
                NO_MISTAKES_DETECTED

                IMPORTANT:
                - Only identify mistakes that are CLEARLY demonstrated in the student's answer
                - DO NOT FLAG OUT MISTAKES IF YOU ARE NOT ABSOLUTELY SURE THAT IT IS EXACTLY THIS MISTAKE. IT IS MUCH BETTER TO NOT FLAG OUT THAN FLAG OUT WRONGLY.
                - If the suggested mistake is not actually wrong in this context, do not flag it
                - Be conservative - only flag obvious instances of the listed mistakes
                - Do not include any other text or explanations

                STUDENT'S ANSWER: {user_answer}
                """

                # Call Kimi K2 for mistake detection
                mistake_completion = groq_client.chat.completions.create(
                    model="moonshotai/kimi-k2-instruct",
                    messages=[
                        {"role": "system", "content": "You are an expert at identifying common student misconceptions in chemistry answers."},
                        {"role": "user", "content": mistake_detection_prompt}
                    ],
                    temperature=0.01,
                )

                mistake_response = mistake_completion.choices[0].message.content.strip()
                app_logger.debug(f"Mistake detection response for marking point {mp_data['id']}: {mistake_response}")

                # Parse detected mistakes
                if "MISTAKE_DETECTED:" in mistake_response:
                    for line in mistake_response.split('\n'):
                        if line.strip().startswith("MISTAKE_DETECTED:"):
                            try:
                                mistake_num = int(line.split(':')[1].strip()) - 1  # Convert to 0-based index
                                if 0 <= mistake_num < len(common_mistakes):
                                    detected_mistake = common_mistakes[mistake_num]
                                    detected_mistakes.append(detected_mistake)
                                    print(f"🚨 COMMON MISTAKE DETECTED - Question {part_data.question_id}, Part {part_data.id}, MP {mp_data['id']}: {detected_mistake[:100]}...")
                                    app_logger.info(f"Common mistake detected for Q{part_data.question_id}P{part_data.id}MP{mp_data['id']}: {detected_mistake}")
                            except (ValueError, IndexError):
                                app_logger.warning(f"Could not parse mistake number from: {line}")

        except Exception as e_mp:
            app_logger.exception(f"Error evaluating marking point {mp_data['id']} with Kimi K2: {str(e_mp)}")
            error_mp = True

        # Generate feedback with bolded missed parts for partial answers
        feedback_text = mp_data['description']
        if is_partial_mp and missed_snippets:
            feedback_text = generate_missed_feedback_with_bold(mp_data['description'], missed_snippets)

        # Return result
        return {
            'id': mp_data['id'],
            'description': mp_data['description'],
            'score': mp_data['score'],
            'achieved': is_correct_mp,
            'partial': is_partial_mp,
            'achieved_score': point_score,
            'evidence': evidence_mp if 'evidence_mp' in locals() else None,
            'evidence_snippets': evidence_snippets,  # List of evidence snippets for highlighting
            'missed_snippets': missed_snippets,  # List of missed content for partial answers
            'feedback': feedback_text,  # Return the marking point description with bolded missed parts
            'color': assigned_border_class if (is_correct_mp or is_partial_mp) and evidence_snippets else None,
            'error': error_mp,
            'mp_index': mp_index,  # Include index for ordering results
            'detected_mistakes': detected_mistakes  # Include detected common mistakes
        }

    # --- IMPORTANT: GRADING FUNCTION! ---
    def _calculate_score_and_evaluated_points(user_answer: str, part_data: Part, gemini_model, app_logger):
        """
        Calculates the score for a given part and returns evaluated marking points.
        This logic is shared between get_git_diff and submit_problemset.
        Returns a dictionary with 'score', 'evaluated_points', 'evidence_list', and 'timing'.
        The 'evidence_list' contains structured evidence data for code-based highlighting.
        """
        # Initialize timing tracker
        timer = GradingTimer()
        timer.start_step("Initialization and Setup")

        total_score = 0
        evaluated_points = []

        if part_data.input_type == 'mcq':
            timer.start_step("MCQ Processing")
            # For MCQ, the answer is the selected option index (passed as user_answer)
            if not user_answer: # Should be validated before calling this helper
                timer.start_step("MCQ Error - No Answer")
                timing_summary = timer.get_summary()
                return {'score': 0, 'evaluated_points': [], 'evidence_list': [], 'error': 'No answer provided for MCQ', 'timing': timing_summary}

            try:
                timer.start_step("MCQ Validation")
                selected_option_index = int(user_answer)
                options = part_data.options

                if not options or selected_option_index >= len(options) or selected_option_index < 0:
                    timer.start_step("MCQ Error - Invalid Option")
                    timing_summary = timer.get_summary()
                    return {'score': 0, 'evaluated_points': [], 'evidence_list': [], 'error': 'Invalid option selected for MCQ', 'timing': timing_summary}

                timer.start_step("MCQ Scoring")
                selected_option = options[selected_option_index]
                is_correct = selected_option.is_correct # Assuming is_correct is a boolean field
                total_score = part_data.score if is_correct else 0

                timer.start_step("MCQ Feedback Generation")
                # For MCQs, create structured feedback based on correctness
                if is_correct:
                    feedback_text = f"Correctly identified {selected_option.description}"
                else:
                    # Find the correct option for feedback
                    correct_option = next((opt for opt in options if opt.is_correct), None)
                    if correct_option:
                        feedback_text = f"Incorrectly explained - selected '{selected_option.description}' instead of '{correct_option.description}'"
                    else:
                        feedback_text = f"Incorrectly explained - selected '{selected_option.description}'"

                timer.start_step("MCQ Result Compilation")
                evaluated_points.append({
                    'id': f"mcq_{part_data.id}",
                    'description': f"Selected option: {selected_option.description}",
                    'score': part_data.score,
                    'achieved': is_correct,
                    'partial': False,
                    'achieved_score': total_score,
                    'evidence': str(selected_option_index),
                    'feedback': feedback_text,
                    'color': 'border-green-400' if is_correct else 'border-red-400'
                })

                timing_summary = timer.get_summary()
                return {
                    'score': total_score,
                    'evaluated_points': evaluated_points,
                    'evidence_list': [],  # MCQ doesn't need evidence highlighting
                    'timing': timing_summary
                }

            except (ValueError, TypeError) as e:
                timer.start_step("MCQ Error - Exception")
                app_logger.exception(f"Error processing MCQ answer in helper: {str(e)}")
                timing_summary = timer.get_summary()
                return {'score': 0, 'evaluated_points': [], 'evidence_list': [], 'error': 'Invalid MCQ answer format', 'timing': timing_summary}

        # For non-MCQ questions (free response with marking points)
        timer.start_step("Free Response Setup")
        try:
            marking_points_data = [{
                'id': mp.id,
                'description': mp.description,
                'score': mp.score
            } for mp in part_data.marking_points]

            highlight_border_classes = [
                'border-yellow-400', 'border-blue-400', 'border-green-400',
                'border-pink-400', 'border-purple-400', 'border-indigo-400',
                'border-teal-400', 'border-orange-400', 'border-lime-400',
                'border-cyan-400'
            ]
            color_index = 0

            timer.start_step(f"Processing {len(marking_points_data)} Marking Points in Parallel")
            parallel_start_time = time.time()

            # Process all marking points in parallel
            with ThreadPoolExecutor(max_workers=min(len(marking_points_data), 10)) as executor:
                # Prepare tasks for parallel execution
                future_to_mp = {}
                for mp_index, mp_data in enumerate(marking_points_data):
                    assigned_border_class = highlight_border_classes[color_index % len(highlight_border_classes)]
                    color_index += 1

                    # Submit task to thread pool
                    future = executor.submit(
                        _process_single_marking_point,
                        mp_data, mp_index, user_answer, part_data, marking_points_data,
                        app_logger, assigned_border_class
                    )
                    future_to_mp[future] = mp_index

                # Collect results as they complete
                results = [None] * len(marking_points_data)  # Pre-allocate list to maintain order
                completed_count = 0
                for future in as_completed(future_to_mp):
                    try:
                        result = future.result()
                        mp_index = result['mp_index']
                        results[mp_index] = result
                        completed_count += 1
                        app_logger.debug(f"Completed marking point {mp_index + 1}/{len(marking_points_data)} in parallel")
                    except Exception as e:
                        mp_index = future_to_mp[future]
                        app_logger.exception(f"Error in parallel processing of marking point {mp_index}: {str(e)}")
                        # Create error result
                        mp_data = marking_points_data[mp_index]
                        results[mp_index] = {
                            'id': mp_data['id'],
                            'description': mp_data['description'],
                            'score': mp_data['score'],
                            'achieved': False,
                            'partial': False,
                            'achieved_score': 0,
                            'evidence': None,
                            'feedback': mp_data['description'],
                            'color': None,
                            'error': True,
                            'mp_index': mp_index
                        }
                        completed_count += 1

            parallel_end_time = time.time()
            parallel_duration = parallel_end_time - parallel_start_time

            # Add parallel processing timing information
            timer.steps.append({
                'name': f"Parallel Processing of {len(marking_points_data)} Marking Points",
                'duration_ms': round(parallel_duration * 1000, 2),
                'duration_s': round(parallel_duration, 3),
                'details': f"Processed {len(marking_points_data)} marking points concurrently"
            })

            timer.start_step("Result Compilation and Score Calculation")
            # Calculate total score and prepare evaluated points
            total_score = 0
            evaluated_points = []
            for result in results:
                if result:  # Ensure result is not None
                    total_score += result['achieved_score']
                    # Remove mp_index from result before adding to evaluated_points
                    result_copy = result.copy()
                    result_copy.pop('mp_index', None)
                    evaluated_points.append(result_copy)

            timer.start_step("Final Score Compilation and Evidence List Creation")
            # Create evidence list for code-based highlighting
            evidence_list = []
            print("EVAL POINTS:", evaluated_points)
            for point in evaluated_points:
                if (point.get('achieved') or point.get('partial')) and point.get('evidence_snippets'):
                    evidence_list.append({
                        'marking_point_id': point['id'],
                        'evidence_snippets': point['evidence_snippets'],
                        'color_class': point['color'],
                        'achieved': point['achieved'],
                        'partial': point['partial']
                    })

            timing_summary = timer.get_summary()
            return {
                'score': total_score,
                'evaluated_points': evaluated_points,
                'evidence_list': evidence_list,
                'timing': timing_summary
            }

        except Exception as e:
            timer.start_step("Error Handling - Free Response")
            app_logger.exception(f"Error processing free-response answer in helper: {str(e)}")
            timing_summary = timer.get_summary()
            # Return 0 score and empty points if a major error occurs in this block
            return {'score': 0, 'evaluated_points': [], 'evidence_list': [], 'error': 'Error processing marking points', 'timing': timing_summary}
    # --- END: Helper function for grading ---

    def _calculate_score_and_evaluated_points_kimi(user_answer: str, part_data: Part, app_logger):
        """
        Calculates the score for a given part using Kimi K2 and returns evaluated marking points.
        This is the Kimi K2 version of the grading function.
        Returns a dictionary with 'score', 'evaluated_points', 'evidence_list', and 'timing'.
        """
        # Initialize timing tracker
        timer = GradingTimer()
        timer.start_step("Initialization and Setup")

        total_score = 0
        evaluated_points = []

        if part_data.input_type == 'mcq':
            timer.start_step("MCQ Processing")
            # For MCQ, the answer is the selected option index (passed as user_answer)
            if not user_answer:
                timer.start_step("MCQ Error - No Answer")
                timing_summary = timer.get_summary()
                return {'score': 0, 'evaluated_points': [], 'evidence_list': [], 'error': 'No answer provided for MCQ', 'timing': timing_summary}

            try:
                timer.start_step("MCQ Validation")
                selected_option_index = int(user_answer)
                options = part_data.options

                if not options or selected_option_index >= len(options) or selected_option_index < 0:
                    timer.start_step("MCQ Error - Invalid Option")
                    timing_summary = timer.get_summary()
                    return {'score': 0, 'evaluated_points': [], 'evidence_list': [], 'error': 'Invalid option selected for MCQ', 'timing': timing_summary}

                timer.start_step("MCQ Scoring")
                selected_option = options[selected_option_index]
                is_correct = selected_option.is_correct
                total_score = part_data.score if is_correct else 0

                timer.start_step("MCQ Feedback Generation")
                if is_correct:
                    feedback_text = f"Correctly identified {selected_option.description}"
                else:
                    correct_option = next((opt for opt in options if opt.is_correct), None)
                    if correct_option:
                        feedback_text = f"Incorrectly explained - selected '{selected_option.description}' instead of '{correct_option.description}'"
                    else:
                        feedback_text = f"Incorrectly explained - selected '{selected_option.description}'"

                timer.start_step("MCQ Result Compilation")
                evaluated_points.append({
                    'id': f"mcq_{part_data.id}",
                    'description': f"Selected option: {selected_option.description}",
                    'score': part_data.score,
                    'achieved': is_correct,
                    'partial': False,
                    'achieved_score': total_score,
                    'evidence': str(selected_option_index),
                    'feedback': feedback_text,
                    'color': 'border-green-400' if is_correct else 'border-red-400'
                })

                timing_summary = timer.get_summary()
                return {
                    'score': total_score,
                    'evaluated_points': evaluated_points,
                    'evidence_list': [],
                    'timing': timing_summary
                }

            except (ValueError, TypeError) as e:
                timer.start_step("MCQ Error - Exception")
                app_logger.exception(f"Error processing MCQ answer with Kimi K2: {str(e)}")
                timing_summary = timer.get_summary()
                return {'score': 0, 'evaluated_points': [], 'evidence_list': [], 'error': 'Invalid MCQ answer format', 'timing': timing_summary}

        # For non-MCQ questions (free response with marking points)
        timer.start_step("Free Response Setup")
        try:
            marking_points_data = [{
                'id': mp.id,
                'description': mp.description,
                'score': mp.score
            } for mp in part_data.marking_points]

            highlight_border_classes = [
                'border-yellow-400', 'border-blue-400', 'border-green-400',
                'border-pink-400', 'border-purple-400', 'border-indigo-400',
                'border-teal-400', 'border-orange-400', 'border-lime-400',
                'border-cyan-400'
            ]
            color_index = 0

            timer.start_step(f"Processing {len(marking_points_data)} Marking Points in Parallel with Kimi K2")
            parallel_start_time = time.time()

            # Process all marking points in parallel using Kimi K2
            with ThreadPoolExecutor(max_workers=min(len(marking_points_data), 10)) as executor:
                future_to_mp = {}
                for mp_index, mp_data in enumerate(marking_points_data):
                    assigned_border_class = highlight_border_classes[color_index % len(highlight_border_classes)]
                    color_index += 1

                    # Get manual adjustment context before submitting to thread pool
                    manual_adjustment_context = _get_manual_adjustment_context(mp_data['id'], user_answer, app_logger)

                    # Submit task to thread pool using Kimi function
                    future = executor.submit(
                        _process_single_marking_point_kimi,
                        mp_data, mp_index, user_answer, part_data, marking_points_data,
                        app_logger, assigned_border_class, manual_adjustment_context
                    )
                    future_to_mp[future] = mp_index

                # Collect results as they complete
                results = [None] * len(marking_points_data)
                completed_count = 0
                for future in as_completed(future_to_mp):
                    try:
                        result = future.result()
                        mp_index = result['mp_index']
                        results[mp_index] = result
                        completed_count += 1
                        app_logger.debug(f"Completed Kimi K2 marking point {mp_index + 1}/{len(marking_points_data)} in parallel")
                    except Exception as e:
                        mp_index = future_to_mp[future]
                        app_logger.exception(f"Error in parallel processing of marking point {mp_index} with Kimi K2: {str(e)}")
                        mp_data = marking_points_data[mp_index]
                        results[mp_index] = {
                            'id': mp_data['id'],
                            'description': mp_data['description'],
                            'score': mp_data['score'],
                            'achieved': False,
                            'partial': False,
                            'achieved_score': 0,
                            'evidence': None,
                            'feedback': mp_data['description'],
                            'color': None,
                            'error': True,
                            'mp_index': mp_index
                        }
                        completed_count += 1

            parallel_end_time = time.time()
            parallel_duration = parallel_end_time - parallel_start_time

            timer.steps.append({
                'name': f"Kimi K2 Parallel Processing of {len(marking_points_data)} Marking Points",
                'duration_ms': round(parallel_duration * 1000, 2),
                'duration_s': round(parallel_duration, 3),
                'details': f"Processed {len(marking_points_data)} marking points concurrently with Kimi K2"
            })

            timer.start_step("Result Compilation and Score Calculation")
            total_score = 0
            evaluated_points = []
            all_detected_mistakes = []

            for result in results:
                if result:
                    total_score += result['achieved_score']
                    result_copy = result.copy()
                    result_copy.pop('mp_index', None)

                    # Collect detected mistakes
                    if result.get('detected_mistakes'):
                        all_detected_mistakes.extend(result['detected_mistakes'])

                    evaluated_points.append(result_copy)

            timer.start_step("Final Score Compilation and Evidence List Creation")
            evidence_list = []
            for point in evaluated_points:
                if (point.get('achieved') or point.get('partial')) and point.get('evidence_snippets'):
                    evidence_list.append({
                        'evidence_snippets': point['evidence_snippets'],
                        'color_class': point.get('color', ''),
                        'achieved': point.get('achieved', False),
                        'partial': point.get('partial', False)
                    })

            # Remove duplicate mistakes while preserving order
            unique_mistakes = []
            for mistake in all_detected_mistakes:
                if mistake not in unique_mistakes:
                    unique_mistakes.append(mistake)

            # Debug logging for final detected mistakes
            if unique_mistakes:
                print(f"📋 FINAL MISTAKES SUMMARY - Question {part_data.question_id}, Part {part_data.id}: {len(unique_mistakes)} unique mistakes detected")
                for i, mistake in enumerate(unique_mistakes, 1):
                    print(f"   {i}. {mistake[:80]}...")
                app_logger.info(f"Final mistakes for Q{part_data.question_id}P{part_data.id}: {len(unique_mistakes)} unique mistakes")
            else:
                print(f"✅ NO MISTAKES DETECTED - Question {part_data.question_id}, Part {part_data.id}")

            timing_summary = timer.get_summary()
            return {
                'score': total_score,
                'evaluated_points': evaluated_points,
                'evidence_list': evidence_list,
                'detected_mistakes': unique_mistakes,
                'timing': timing_summary
            }

        except Exception as e:
            timer.start_step("Error Handling - Free Response")
            app_logger.exception(f"Error processing free-response answer with Kimi K2: {str(e)}")
            timing_summary = timer.get_summary()
            return {'score': 0, 'evaluated_points': [], 'evidence_list': [], 'error': 'Error processing marking points with Kimi K2', 'timing': timing_summary}

    # --- START: New Supabase Auth Confirmation Route ---
    @app.route('/api/auth/confirm', methods=['GET'])
    def confirm_auth():
        """
        Handles email confirmation links (e.g., password reset, email change) using Supabase PKCE flow.
        Verifies the token_hash and type, or exchanges code for session, and redirects.
        """
        # Check for code parameter (new PKCE flow)
        code = request.args.get('code')
        if code:
            app_logger.info(f"Auth confirmation request received with code parameter")
            next_url = request.args.get('next', url_for('login'))  # Default redirect to login

            try:
                # Access the Supabase client
                from app import supabase

                if not supabase:
                    error_logger.critical("Supabase client not available in confirm_auth route.")
                    flash('Authentication system configuration error.', 'error')
                    return redirect(url_for('login'))

                # Exchange the code for a session
                app_logger.info(f"Attempting to exchange code for session...")
                exchange_response = supabase.auth.exchange_code_for_session({
                    "auth_code": code
                })
                app_logger.info(f"Code exchange response: {exchange_response}")

                # Check if exchange was successful
                if exchange_response and exchange_response.user:
                    supabase_email = exchange_response.user.email
                    app_logger.info(f"Successfully exchanged code for session. User email: {supabase_email}")

                    # Store the email in session
                    session['supabase_email'] = supabase_email
                    session['auth_timestamp'] = datetime.now().timestamp()
                    session.permanent = True
                    session.modified = True

                    # Validate the next_url to prevent open redirect vulnerabilities
                    if next_url and next_url.startswith('/'):
                        # Flash success message
                        flash('Verification successful! Please set your new password.', 'success')
                        return redirect(next_url)
                    else:
                        # Invalid 'next' URL provided
                        error_logger.warning(f"Invalid 'next' URL provided: {next_url}. Redirecting to login.")
                        flash('Verification successful! Please log in.', 'success')
                        return redirect(url_for('login'))
                else:
                    error_logger.error(f"Code exchange failed. Response: {exchange_response}")
                    flash('Verification failed. The link may be invalid or expired.', 'error')
                    return redirect(url_for('login'))

            except Exception as e:
                error_logger.exception(f"Error during code exchange: {str(e)}")
                flash('An unexpected error occurred during verification.', 'error')
                return redirect(url_for('login'))

        # Legacy flow with token_hash
        token_hash = request.args.get('token_hash')
        auth_type = request.args.get('type')
        next_url = request.args.get('next', url_for('login')) # Default redirect to login

        if token_hash and auth_type:
            app_logger.info(f"Auth confirmation request received. Type: {auth_type}, Token Hash: {token_hash[:5]}..., Next: {next_url}")

            try:
                # Access the Supabase client
                from app import supabase

                if not supabase:
                    error_logger.critical("Supabase client not available in confirm_auth route.")
                    flash('Authentication system configuration error.', 'error')
                    return redirect(url_for('login'))

                app_logger.info(f"Attempting Supabase verify_otp with type: {auth_type}, token_hash: {token_hash[:5]}...")
                # Verify the OTP (which includes password reset tokens)
                # Get the email from the query parameters if available
                email = request.args.get('email')

                # Use the correct schema for verifying OTP
                verify_data = {
                    "token_hash": token_hash,
                    "type": auth_type
                }

                # Add email to the verification data if available
                if email:
                    verify_data["email"] = email
                    app_logger.info(f"Including email in OTP verification: {email}")
                else:
                    app_logger.info(f"Email not found in query parameters, proceeding without it")

                response = supabase.auth.verify_otp(verify_data)
                app_logger.info(f"Supabase verify_otp response: {response}")

                # Check if verification was successful (user data should be present)
                if response and response.user:
                    supabase_email = response.user.email
                    app_logger.info(f"Supabase user verified: {supabase_email}")

                    # Validate the next_url to prevent open redirect vulnerabilities
                    # Allow only relative paths starting with '/'
                    if next_url and next_url.startswith('/'):
                        # Store the Supabase email and token_hash in the session for use in the reset_password route
                        session['supabase_email'] = supabase_email
                        session['token_hash'] = token_hash
                        session['auth_timestamp'] = datetime.now().timestamp()  # Add timestamp for session freshness check
                        session.permanent = True  # Make session persistent
                        session.modified = True  # Mark session as modified to ensure it's saved

                        # Add the email and token to the redirect URL as query parameters as a fallback
                        # URL encode the parameters to ensure they're properly formatted
                        encoded_email = urllib.parse.quote(supabase_email)
                        encoded_token = urllib.parse.quote(token_hash)
                        redirect_url = f"{next_url}?email={encoded_email}&token_hash={encoded_token}"
                        app_logger.info(f"Adding email and token to redirect URL as fallback: {redirect_url}")

                        # Flash success message
                        flash('Verification successful! Please set your new password.', 'success')
                        return redirect(redirect_url) # Redirect to the URL with query parameters as fallback
                    else:
                        # Invalid 'next' URL provided
                        error_logger.warning(f"Invalid 'next' URL provided in confirmation link: {next_url}. Redirecting verified user {supabase_email} to login.")
                        flash('Verification link error. Redirecting to login.', 'warning')
                        return redirect(url_for('login'))
                else:
                    error_logger.error(f"Supabase verify_otp failed for type {auth_type}. Response: {response}")
                    flash('Verification failed. The link may be invalid or expired.', 'error')
                    return redirect(url_for('login'))

            except Exception as e:
                # Catch potential exceptions from Supabase client or other issues
                error_logger.exception(f"Error during auth confirmation ({auth_type}): {str(e)}")
                # Check for specific Supabase errors if possible (e.g., invalid token)
                if "invalid token" in str(e).lower():
                    flash('Verification failed: Invalid or expired link.', 'error')
                else:
                    flash('An unexpected error occurred during verification.', 'error')
                return redirect(url_for('login'))

        # If we get here, neither code nor token_hash+type were provided
        error_logger.warning("Auth confirmation missing required parameters (code or token_hash+type).")
        flash('Invalid or incomplete confirmation link.', 'error')
        return redirect(url_for('login'))
    # --- END: New Supabase Auth Confirmation Route ---


    # --- Other API Endpoints ---

    @app.route("/check_answer/<int:question_id>/<int:part_id>", methods=['POST'])
    @limiter.limit("30/minute") # Apply rate limit
    @login_required # Require login to check answers
    def check_answer(question_id, part_id):
        if 'user_id' not in session:
            error_logger.warning("Unauthorized attempt to submit answer")
            return jsonify({
                'status': 'error',
                'message': 'Please login to submit answers'
            }), 401

        update_user_activity(session['user_id'])
        part_data = Part.query.get_or_404(part_id)

        # Handle both text and image inputs
        user_answer = request.form.get('answer', '').strip()
        image_file = request.files.get('image')
        confidence_level = request.form.get('confidence_level', 'Medium')

        if not user_answer and not image_file:
            error_logger.info(f"Empty answer submitted - User ID: {session['user_id']}, Question ID: {question_id}, Part ID: {part_id}")
            return jsonify({
                'status': 'error',
                'message': 'Please provide an answer or upload an image'
            }), 400

        try:
            # Process image if provided
            if image_file:
                # Validate file type
                if not image_file.filename.lower().endswith(('.png', '.jpg', '.jpeg')):
                    return jsonify({
                        'status': 'error',
                        'message': 'Invalid file type. Please upload a PNG, JPG, or JPEG image.'
                    }), 400

                # Encode the image to base64
                image_data = image_file.read()
                base64_image = base64.b64encode(image_data).decode('utf-8')

                # Initialize Gemini vision model
                gemini_vision_model = genai.GenerativeModel('gemini-2.5-pro')

                # Create multimodal prompt
                prompt_parts = [
                    "Convert this mathematical image to LaTeX format. Return ONLY the LaTeX code without any additional text, comments, or formatting. Include ALL mathematical content from the image.",
                    {"mime_type": "image/jpeg", "data": base64_image}
                ]

                # Generate content
                response = gemini_vision_model.generate_content(prompt_parts)
                user_answer = response.text
                app_logger.info(f"Image received for OCR - User ID: {session['user_id']}, Question ID: {question_id}, Part ID: {part_id}")

            # Use Kimi K2 if available, otherwise fall back to Gemini
            if is_kimi_k2_available():
                grading_details = _calculate_score_and_evaluated_points_kimi(user_answer, part_data, app_logger)
                app_logger.info("Using Kimi K2 for grading")
            else:
                grading_details = _calculate_score_and_evaluated_points(user_answer, part_data, gemini_grading_client, app_logger)
                app_logger.info("Falling back to Gemini for grading (Kimi K2 not available)")
            current_score = grading_details['score']
            feedback_to_user = "Your answer has been submitted and graded."

            # Store evaluation details as JSON in feedback field
            import json
            feedback_data = {
                'evaluated_points': grading_details.get('evaluated_points', []),
                'timing': grading_details.get('timing', {}),
                'total_score': current_score,
                'max_score': part_data.score,
                'evidence_list': grading_details.get('evidence_list', []),
                'detected_mistakes': grading_details.get('detected_mistakes', [])
            }
            feedback_json = json.dumps(feedback_data)

            submission = Submission(
                user_id=session['user_id'],
                question_id=question_id,
                part_id=part_id,
                answer=user_answer,
                score=current_score,
                feedback=feedback_json
            )
            db.session.add(submission)
            db.session.commit()

            # Trigger feed generation for the user
            try:
                from routes.feed_utils import trigger_feed_generation_for_user
                trigger_feed_generation_for_user(session['user_id'])
            except Exception as e:
                app_logger.warning(f"Failed to generate feed posts for user {session['user_id']}: {e}")

            app_logger.info(
                f"Answer submitted - User ID: {session['user_id']}, "
                f"Question ID: {question_id}, Part ID: {part_id}, "
                f"Score: {submission.score}/{part_data.score}"
            )

            return jsonify({
                'status': 'success',
                'feedback': feedback_to_user,
                'score': submission.score,
                'submission_id': submission.id,
                'max_score': part_data.score,
                'detected_mistakes': grading_details.get('detected_mistakes', [])
            })

        except Exception as e:
            db.session.rollback()
            error_logger.exception(
                f"Error processing submission - User ID: {session['user_id']}, "
                f"Question ID: {question_id}, Part ID: {part_id}, Error: {str(e)}"
            )
            return jsonify({
                'status': 'error',
                'message': 'An error occurred while processing your answer'
            }), 500



    @app.route('/api/test_missed_functionality/<int:question_id>/<int:part_id>', methods=['POST'])
    @login_required
    @limiter.limit("10/minute")
    def test_missed_functionality(question_id, part_id):
        """Test endpoint to verify MISSED functionality is working"""
        if 'user_id' not in session:
            return jsonify({'status': 'error', 'message': 'User not logged in'}), 401

        try:
            part_data = Part.query.get_or_404(part_id)
            user_answer = request.form.get('answer', '').strip()

            if not user_answer:
                return jsonify({'status': 'error', 'message': 'No answer provided'}), 400

            # Use Kimi K2 if available
            if is_kimi_k2_available():
                grading_details = _calculate_score_and_evaluated_points_kimi(user_answer, part_data, app.logger)
                app.logger.info("Using Kimi K2 for testing MISSED functionality")
            else:
                return jsonify({'status': 'error', 'message': 'Kimi K2 not available for MISSED functionality'}), 400

            # Extract missed data from evaluated points
            missed_data = []
            for point in grading_details.get('evaluated_points', []):
                if point.get('partial') and point.get('missed_snippets'):
                    missed_data.append({
                        'marking_point_id': point.get('id'),
                        'description': point.get('description'),
                        'missed_snippets': point.get('missed_snippets'),
                        'feedback_with_bold': point.get('feedback')
                    })

            return jsonify({
                'status': 'success',
                'missed_data': missed_data,
                'total_partial_points': len([p for p in grading_details.get('evaluated_points', []) if p.get('partial')]),
                'total_points_with_missed': len(missed_data)
            })

        except Exception as e:
            app.logger.exception(f"Error in test_missed_functionality: {str(e)}")
            return jsonify({'status': 'error', 'message': 'Internal server error'}), 500

    @app.route('/get_git_diff/<int:question_id>/<int:part_id>', methods=['POST'])
    @login_required
    @limiter.limit("30/minute")
    def get_git_diff(question_id, part_id):
        if 'user_id' not in session:
            return jsonify({
                'status': 'error',
                'message': 'Please login to submit answers'
            }), 401

        update_user_activity(session['user_id'])
        part_data = Part.query.get_or_404(part_id)
        question_data = Question.query.get_or_404(question_id)

        # Handle both text and image submissions
        user_answer = request.form.get('answer', '').strip()
        image_file = request.files.get('image')

        # If an image was submitted, process it
        if image_file and image_file.filename:
            try:
                # Convert image to base64 for API processing
                image_data = image_file.read()
                base64_image = base64.b64encode(image_data).decode('utf-8')

                # Initialize Gemini vision model
                gemini_vision_model = genai.GenerativeModel('gemini-2.5-pro-preview-05-06')

                # Create multimodal prompt
                prompt_parts = [
                    "You are a LaTeX expert. Convert this mathematical image to LaTeX format. Return ONLY the LaTeX code without any additional text, comments, or formatting. Include ALL mathematical content from the image.",
                    {"mime_type": "image/jpeg", "data": base64_image}
                ]

                # Generate content
                response = gemini_vision_model.generate_content(prompt_parts)
                user_answer = response.text
            except Exception as e:
                error_logger.exception(f"Error processing image submission: {str(e)}")
                return jsonify({
                    'status': 'error',
                    'message': 'Error processing image submission'
                }), 500

        if not user_answer:
            return jsonify({
                'status': 'error',
                'message': 'Please provide an answer'
            }), 400

        # Use Kimi K2 if available, otherwise fall back to Gemini
        if is_kimi_k2_available():
            grading_details = _calculate_score_and_evaluated_points_kimi(user_answer, part_data, app_logger)
            app_logger.info("Using Kimi K2 for grading")
        else:
            grading_details = _calculate_score_and_evaluated_points(user_answer, part_data, gemini_grading_client, app_logger)
            app_logger.info("Falling back to Gemini for grading (Kimi K2 not available)")
        print(grading_details)
        total_score = grading_details['score']
        evaluated_points = grading_details['evaluated_points']

        if 'error' in grading_details and part_data.input_type == 'mcq': # Handle MCQ specific errors from helper
            return jsonify({'status': 'error', 'message': grading_details['error']}), 400

        # If it's an MCQ, the helper already determined the score and basic feedback.
        # We can return a simplified response for MCQs directly after calling the helper.
        if part_data.input_type == 'mcq':
            # Store evaluation details as JSON in feedback field
            import json
            feedback_data = {
                'evaluated_points': grading_details.get('evaluated_points', []),
                'timing': grading_details.get('timing', {}),
                'total_score': total_score,
                'max_score': part_data.score,
                'evidence_list': grading_details.get('evidence_list', []),  # Include evidence_list for MCQ too
                'detected_mistakes': grading_details.get('detected_mistakes', [])
            }
            feedback_json = json.dumps(feedback_data)

            # Create submission record
            submission = Submission(
                user_id=session['user_id'],
                question_id=question_id,
                part_id=part_id,
                answer=user_answer,  # user_answer is the selected_option_index for MCQ
                score=total_score,
                feedback=feedback_json
            )
            db.session.add(submission)
            db.session.commit()

            is_correct_mcq = total_score > 0
            return jsonify({
                'status': 'success',
                'score': total_score,
                'max_score': part_data.score,
                'is_correct': is_correct_mcq,
                'feedback': 'Your answer is correct.' if is_correct_mcq else 'Your answer is incorrect.',
                'marking_points': evaluated_points, # Contains the single MCQ point
                'timing': grading_details.get('timing', {}),  # Include timing information
                'detected_mistakes': grading_details.get('detected_mistakes', [])  # Include detected mistakes
            })

        # For non-MCQ, proceed with database operations and detailed response
        try:
            import json

            # Extract context-dependent data
            current_user_id = session['user_id']

            # Prepare feedback data for storage
            feedback_data = {
                'evaluated_points': grading_details.get('evaluated_points', []),
                'timing': grading_details.get('timing', {}),
                'total_score': total_score,
                'max_score': part_data.score,
                'detected_mistakes': grading_details.get('detected_mistakes', [])
            }

            # Store evaluation details as JSON in feedback field
            feedback_json = json.dumps(feedback_data)

            # Create a new submission record in the database
            submission = Submission(
                user_id=current_user_id,
                question_id=question_id,
                part_id=part_id,
                answer=user_answer, # Store original answer
                score=total_score,
                feedback=feedback_json
            )

            # Add and commit to the database
            db.session.add(submission)
            db.session.commit()

            # Trigger feed generation for the user
            try:
                from routes.feed_utils import trigger_feed_generation_for_user
                trigger_feed_generation_for_user(current_user_id)
            except Exception as e:
                app_logger.warning(f"Failed to generate feed posts for user {current_user_id}: {e}")

            app_logger.info(f"Database operations completed for user {current_user_id}")

            # Use the evidence_list from the grading result for highlighting
            evidence_list = grading_details.get('evidence_list', [])
            print(evidence_list)

            # Store evidence_list in the feedback data for later use by highlighted_answer
            feedback_data['evidence_list'] = evidence_list
            feedback_json = json.dumps(feedback_data)

            # Update the submission with the evidence_list
            submission.feedback = feedback_json
            db.session.commit()

            return jsonify({
                'status': 'success',
                'marking_points': evaluated_points,
                'score': total_score,
                'max_score': part_data.score,
                'timing': grading_details.get('timing', {}),  # Include timing information
                'evidence_list': evidence_list,  # Include evidence_list for code-based highlighting
                'detected_mistakes': grading_details.get('detected_mistakes', [])  # Include detected mistakes
            })

        except Exception as e:
            error_logger.exception(
                f"Error processing answer - User ID: {session['user_id']}, "
                f"Question ID: {question_id}, Part ID: {part_id}, Error: {str(e)}"
            )
            return jsonify({
                'status': 'error',
                'message': 'An error occurred while processing your answer'
            }), 500

    @app.route('/highlighted_answer/<int:question_id>/<int:part_id>', methods=['POST'])
    @login_required
    @limiter.limit("30/minute")
    def highlighted_answer(question_id, part_id):
        """Returns the highlighted answer for a given question and part."""
        if 'user_id' not in session:
            return jsonify({
                'status': 'error',
                'message': 'Please login to get highlighted answer'
            }), 401

        update_user_activity(session['user_id'])
        part_data = Part.query.get_or_404(part_id)

        # Get user_answer from request
        try:
            data = request.get_json()
            if not data:
                return jsonify({
                    'status': 'error',
                    'message': 'No data provided'
                }), 400

            user_answer = data.get('user_answer', '')

            if not user_answer:
                return jsonify({
                    'status': 'error',
                    'message': 'No user answer provided'
                }), 400

        except Exception as e:
            error_logger.exception(f"Error parsing request data: {str(e)}")
            return jsonify({
                'status': 'error',
                'message': 'Invalid request data'
            }), 400

        # Retrieve evidence_list from the most recent submission instead of re-evaluating
        try:
            # Get the most recent submission for this user, question, and part
            latest_submission = Submission.query.filter_by(
                user_id=session['user_id'],
                question_id=question_id,
                part_id=part_id
            ).order_by(Submission.id.desc()).first()

            evidence_list = []
            if latest_submission and latest_submission.feedback:
                import json
                feedback_data = json.loads(latest_submission.feedback)
                evidence_list = feedback_data.get('evidence_list', [])
                app_logger.info(f"Retrieved evidence_list from stored submission: {len(evidence_list)} items")
            else:
                app_logger.warning("No stored submission found or no evidence_list in feedback, falling back to re-evaluation")
                # Fallback: re-evaluate if no stored data is found
                if is_kimi_k2_available():
                    grading_result = _calculate_score_and_evaluated_points_kimi(user_answer, part_data, app_logger)
                    app_logger.info("Using Kimi K2 for re-evaluation")
                else:
                    grading_result = _calculate_score_and_evaluated_points(user_answer, part_data, gemini_grading_client, app_logger)
                    app_logger.info("Falling back to Gemini for re-evaluation (Kimi K2 not available)")
                evidence_list = grading_result.get('evidence_list', [])

        except Exception as e:
            error_logger.exception(f"Error retrieving evidence list from submission: {str(e)}")
            evidence_list = []

        # For MCQ, return simple response
        if part_data.input_type == 'mcq':
            return jsonify({
                'status': 'success',
                'answer': user_answer  # For MCQ, just return the plain answer
            })

        # For non-MCQ, create highlighted answer using code-based highlighting with LLM fallback
        try:
            import html
            import json

            highlighted_answer = html.escape(user_answer)  # Default fallback

            # Try code-based highlighting first
            try:
                highlighted_answer = create_highlighted_answer_with_code(evidence_list, user_answer)
                app_logger.info("Successfully used code-based highlighting")
            except Exception as code_error:
                app_logger.warning(f"Code-based highlighting failed: {str(code_error)}, falling back to LLM")

                # Fallback to LLM-based highlighting
                # Convert evidence_list back to highlight_data format for LLM compatibility
                highlight_data = []
                for evidence_item in evidence_list:
                    evidence_snippets = evidence_item.get('evidence_snippets', [])
                    if evidence_snippets:
                        # Join multiple snippets with " | " for LLM processing
                        evidence_text = ' | '.join(evidence_snippets)
                        border_style = "border-b-2 " if evidence_item.get('achieved') else "border-b-2 border-dashed "
                        highlight_data.append({
                            'evidence': evidence_text,
                            'color_class': border_style + evidence_item.get('color_class', ''),
                            'partial': evidence_item.get('partial', False),
                            'achieved': evidence_item.get('achieved', False)
                        })

                # Only use LLM fallback if we have highlight_data
                if highlight_data:
                    # Create evidence_data for LLM compatibility
                    evidence_data = []
                    for point in highlight_data:
                        evidence_data.append({
                            'evidence': point['evidence'],
                            'partial': point['partial'],
                            'color': point['color_class'],
                        })

                # Create the prompt for highlighting with feedback information
                highlight_prompt = r"""\
                You are an HTML expert. Given a piece of text and a list of evidence snippets with associated Tailwind CSS border classes and feedback, modify the original text by wrapping EACH evidence snippet EXACTLY as it appears in the text with a span tag that includes both highlighting and tooltip feedback.

                **Instructions:**
                1. Find the occurrences of the evidence snippets within the original text. Do NOT modify the user answer (including changing the order of the answer), just highlight the relevant parts.
                2. Wrap each found evidence snippet with a span tag that includes:
                   - The color class for visual highlighting
                   - A title attribute containing the structured feedback for tooltips
                   - Format: <span class="{{color_class}}" title="{{feedback}}">evidence text</span>
                3. If points overlap, you need to deconflict them in the way that makes the most sense.
                4. Output ONLY the final, complete HTML string.
                5. Ensure ALL highlighted data is included and inside the appropriately colored spans with feedback tooltips.
                6. Do NOT write stuff anything extra at the end of your output.
                8. Make sure the title attribute is properly escaped for HTML (replace quotes with &quot; if needed).
                9. Write your final result as: FINAL RESULT: <your_html_output>
                """
                other=f"""**User answer:**
                {user_answer}

                **Highlight Data (Evidence, CSS Class, and Feedback):**
                {json.dumps(evidence_data, indent=2)}

                **Example format for each highlighted section: (Note there may be more than one, you must include all)**
                <span class="border-b-2 border-yellow-400" title="Correctly identified kinetic energy formula">KE = 1/2mv²</span>
                If partial, make it dotted lines.

                **Final HTML Output:**
                """
                highlight_prompt = highlight_prompt + other
                try:
                        # Use Gemini 2.5 Flash for highlighting with strict parameters
                        gemini_pro_model = genai.GenerativeModel('gemini-2.5-flash')

                        generation_config = {
                            "temperature": 0.1,  # Use deterministic output
                        }

                        response = gemini_pro_model.generate_content(
                            highlight_prompt,
                            generation_config=generation_config,
                        )

                        # Check if response was blocked by safety filters
                        if response.candidates and response.candidates[0].finish_reason == 2:  # SAFETY
                            app_logger.warning("Gemini highlighting response blocked by safety filters")
                            highlighted_answer = html.escape(user_answer)
                        elif not response.text:
                            app_logger.warning("Gemini highlighting response has no text content")
                            highlighted_answer = html.escape(user_answer)
                        else:
                            highlighted_answer_raw = response.text.strip()
                            app_logger.info(f"Raw Gemini highlighting response: {highlighted_answer_raw}")

                            if 'FINAL RESULT: ' in highlighted_answer_raw:
                                highlighted_answer_raw = highlighted_answer_raw.split('FINAL RESULT: ')[1]

                                # Basic validation (optional but recommended)
                                if '<span' in highlighted_answer_raw and '</span>' in highlighted_answer_raw:
                                    highlighted_answer = highlighted_answer_raw
                                else:
                                    # Log a warning if the response doesn't look like HTML
                                    app_logger.warning(f"Gemini highlighting response did not seem to contain valid spans: {highlighted_answer_raw}")
                                    # Fallback to the default escaped answer
                                    highlighted_answer = html.escape(user_answer)
                            else:
                                app_logger.warning("Gemini response missing 'FINAL RESULT: ' marker")
                                highlighted_answer = html.escape(user_answer)

                except Exception as e:
                    error_logger.exception(f"Error calling Gemini API for highlighting: {str(e)}")
                    # Fallback to the default escaped answer in case of API error
                    highlighted_answer = html.escape(user_answer)
                else:
                    app_logger.info("No highlight_data available for LLM fallback")

            # Use the highlighted answer
            highlighted_answer_html = highlighted_answer

            return jsonify({
                'status': 'success',
                'answer': highlighted_answer_html
            })

        except Exception as e:
            error_logger.exception(
                f"Error creating highlighted answer - User ID: {session['user_id']}, "
                f"Question ID: {question_id}, Part ID: {part_id}, Error: {str(e)}"
            )
            return jsonify({
                'status': 'error',
                'message': 'An error occurred while creating highlighted answer'
            }), 500


    @app.route('/auto_save', methods=['POST'])
    @login_required
    def auto_save():
        """Saves incomplete progress for a problem set part."""
        user_id = session['user_id']
        data = request.get_json()

        if not data:
            return jsonify({'status': 'error', 'message': 'Invalid request format.'}), 400

        problemset_id = data.get('problemset_id')
        question_id = data.get('question_id')
        part_id = data.get('part_id')
        answer = data.get('answer') # Can be None or empty

        if not all([problemset_id, question_id, part_id]):
            return jsonify({'status': 'error', 'message': 'Missing required data (problemset, question, part IDs).'}), 400

        try:
            # Get or create incomplete submission record with retry logic built into the model method
            submission = IncompleteSubmission.get_or_create(
                problemset_id=problemset_id,
                user_id=user_id,
                question_id=question_id,
                part_id=part_id
            )

            # Update answer and timestamp
            from sqlalchemy.exc import OperationalError
            import time

            max_retries = 3
            retry_delay = 0.1  # seconds

            for attempt in range(max_retries):
                try:
                    submission.answer = answer # Allow None or empty string
                    submission.last_updated = datetime.now()
                    db.session.commit()
                    # app_logger.debug(f"Autosaved progress for User: {user_id}, PS: {problemset_id}, Q: {question_id}, P: {part_id}")
                    return jsonify({'status': 'success'})

                except OperationalError as e:
                    # Handle database locks specifically
                    if "database is locked" in str(e) and attempt < max_retries - 1:
                        db.session.rollback()
                        time.sleep(retry_delay * (attempt + 1))  # Exponential backoff
                        continue
                    else:
                        db.session.rollback()
                        raise

        except Exception as e:
            db.session.rollback()
            error_logger.exception(f"Error during auto_save for User {user_id}, PS {problemset_id}: {e}")
            return jsonify({'status': 'error', 'message': 'Failed to save progress.'}), 500


    @app.route('/get_saved_progress/<int:problemset_id>')
    @login_required
    def get_saved_progress(problemset_id):
        """Retrieves saved incomplete progress for a problem set."""
        user_id = session['user_id']
        try:
            submissions = IncompleteSubmission.query.filter_by(
                problemset_id=problemset_id,
                user_id=user_id
            ).all()

            # Format data for the frontend
            progress_data = [{
                'question_id': sub.question_id,
                'part_id': sub.part_id,
                'answer': sub.answer if sub.answer is not None else '' # Ensure frontend gets string
            } for sub in submissions]

            return jsonify({
                'status': 'success',
                'submissions': progress_data
            })
        except Exception as e:
            error_logger.exception(f"Error fetching saved progress for User {user_id}, PS {problemset_id}: {e}")
            # Return 200 OK but with error status for AJAX handling
            return jsonify({'status': 'error', 'message': 'Could not retrieve saved progress.'}), 200


    @app.route('/submit_problemset', methods=['POST'])
    @limiter.limit("10/minute") # Limit submission frequency
    @login_required
    def submit_problemset():
        if 'user_id' not in session:
            return jsonify({'status': 'error', 'message': 'Not logged in'}), 401

        data = request.get_json()
        problemset_id = data.get('problemset_id')
        submissions = data.get('submissions', [])  # List of {question_id, part_id, answer}

        if not problemset_id:
            return jsonify({'status': 'error', 'message': 'Missing problemset_id'}), 400

        # Initialize the Gemini model once for all parts in this problemset submission

        try:
            # Create a new problemset submission
            problemset_submission = ProblemSetSubmission(
                problemset_id=problemset_id,
                user_id=session['user_id'],
                status='completed',
                submitted_at=datetime.now()
            )
            db.session.add(problemset_submission)
            db.session.flush()  # Get the ID of the new submission

            # Process each answer
            submission_results = []
            for submission_data in submissions:
                question_id = submission_data.get('question_id')
                part_id = submission_data.get('part_id')
                answer = submission_data.get('answer', '').strip()

                if not all([question_id, part_id]):
                    continue

                # Get the part to check its answer and score
                part = Part.query.get(part_id)
                if not part:
                    continue

                # Use Kimi K2 if available, otherwise fall back to Gemini
                if is_kimi_k2_available():
                    grading_details = _calculate_score_and_evaluated_points_kimi(answer, part, app_logger)
                    app_logger.info("Using Kimi K2 for problemset grading")
                else:
                    grading_details = _calculate_score_and_evaluated_points(answer, part, gemini_grading_client, app_logger)
                    app_logger.info("Falling back to Gemini for problemset grading (Kimi K2 not available)")
                current_score = grading_details['score']

                # Store evaluation details as JSON in feedback field
                import json
                feedback_data = {
                    'evaluated_points': grading_details.get('evaluated_points', []),
                    'timing': grading_details.get('timing', {}),
                    'total_score': current_score,
                    'max_score': part.score,
                    'evidence_list': grading_details.get('evidence_list', [])
                }
                feedback_json = json.dumps(feedback_data)

                # Create the submission
                submission = Submission(
                    user_id=session['user_id'],
                    question_id=question_id,
                    part_id=part_id,
                    answer=answer,
                    score=current_score,
                    feedback=feedback_json
                )
                db.session.add(submission)
                problemset_submission.question_submissions.append(submission)

                # Add submission result with feedback
                submission_results.append({
                    'question_id': question_id,
                    'part_id': part_id,
                    'score': current_score,
                    'max_score': part.score,
                    'feedback': "Answer submitted and graded."
                })

            # Calculate scores
            problemset_submission.calculate_scores()

            # Delete incomplete submissions for this problemset
            IncompleteSubmission.query.filter_by(
                problemset_id=problemset_id,
                user_id=session['user_id']
            ).delete()

            db.session.commit()

            # Trigger feed generation for the user after problemset submission
            try:
                from routes.feed_utils import trigger_feed_generation_for_user
                trigger_feed_generation_for_user(session['user_id'])
            except Exception as e:
                app_logger.warning(f"Failed to generate feed posts for user {session['user_id']}: {e}")

            return jsonify({
                'status': 'success',
                'message': 'Problem set submitted successfully',
                'submission_id': problemset_submission.id,
                'submissions': submission_results
            })

        except Exception as e:
            db.session.rollback()
            print(f"Error submitting problem set: {str(e)}")
            return jsonify({'status': 'error', 'message': str(e)}), 500



    # --- Marking Point AJAX Endpoints ---

    @app.route('/extract_marking_points/<int:part_id>', methods=['POST'])
    @login_required # Should be admin_required?
    def extract_marking_points(part_id):
        """Extract marking points from a question part using an API"""
        part = Part.query.get_or_404(part_id)

        try:
            # Here you would call your API to extract marking points
            # This is a placeholder for the API call
            # api_response = your_api_call(part.description, part.answer)

            # For now, we'll create a sample response
            api_response = {
                'marking_points': [
                    {'description': 'Correct formula', 'score': 2.0},
                    {'description': 'Correct substitution', 'score': 1.0},
                    {'description': 'Correct final answer', 'score': 1.0}
                ]
            }

            # Clear existing auto-generated marking points
            MarkingPoint.query.filter_by(
                part_id=part_id,
                is_auto_generated=True
            ).delete()

            # Add new marking points
            for i, point in enumerate(api_response['marking_points']):
                marking_point = MarkingPoint(
                    part_id=part_id,
                    description=point['description'],
                    score=point['score'],
                    order=i,
                    is_auto_generated=True
                )
                db.session.add(marking_point)

            db.session.commit()

            return jsonify({
                'status': 'success',
                'message': 'Marking points extracted successfully',
                'marking_points': [{
                    'id': mp.id,
                    'description': mp.description,
                    'score': mp.score,
                    'order': mp.order
                } for mp in part.marking_points]
            })

        except Exception as e:
            db.session.rollback()
            return jsonify({
                'status': 'error',
                'message': str(e)
            }), 500


    @app.route('/update_marking_point/<int:marking_point_id>', methods=['PUT'])
    @login_required # Should be admin_required?
    def update_marking_point(marking_point_id):
        """Updates a specific marking point."""
        # Add admin check if necessary

        marking_point = MarkingPoint.query.get_or_404(marking_point_id)
        data = request.get_json()

        if not data:
            return jsonify({'status': 'error', 'message': 'Invalid request format.'}), 400

        try:
            updated = False
            if 'description' in data:
                new_desc = data['description'].strip()
                if new_desc: # Don't allow empty description
                     marking_point.description = new_desc
                     updated = True
                else:
                     return jsonify({'status': 'error', 'message': 'Description cannot be empty.'}), 400
            if 'score' in data:
                try:
                    new_score = float(data['score'])
                    if new_score < 0: # Allow 0 score? Let's prevent negative for now.
                         return jsonify({'status': 'error', 'message': 'Score cannot be negative.'}), 400
                    marking_point.score = new_score
                    updated = True
                except (ValueError, TypeError):
                     return jsonify({'status': 'error', 'message': 'Invalid score format.'}), 400
            if 'order' in data:
                 try:
                     marking_point.order = int(data['order'])
                     updated = True
                 except (ValueError, TypeError):
                      return jsonify({'status': 'error', 'message': 'Invalid order format.'}), 400

            if updated:
                # marking_point.validate() # Call validation if defined in model
                marking_point.is_auto_generated = False # Manual edit overrides auto-gen flag
                db.session.commit()
                app_logger.info(f"Updated marking point {marking_point_id}")
                return jsonify({
                    'status': 'success',
                    'message': 'Marking point updated.',
                    'marking_point': { # Return updated data
                        'id': marking_point.id,
                        'description': marking_point.description,
                        'score': marking_point.score,
                        'order': marking_point.order,
                        'is_auto_generated': marking_point.is_auto_generated
                    }
                })
            else:
                 return jsonify({'status': 'info', 'message': 'No changes detected.'})

        except Exception as e:
            db.session.rollback()
            error_logger.exception(f"Error updating marking point {marking_point_id}: {e}")
            return jsonify({'status': 'error', 'message': 'Error updating marking point.'}), 500


    @app.route('/delete_marking_point/<int:marking_point_id>', methods=['DELETE'])
    @login_required # Should be admin_required?
    def delete_marking_point(marking_point_id):
        """Deletes a specific marking point."""
        # Add admin check if necessary

        marking_point = MarkingPoint.query.get_or_404(marking_point_id)
        part_id = marking_point.part_id # Get part ID for logging

        try:
            db.session.delete(marking_point)
            db.session.commit()
            app_logger.info(f"Deleted marking point {marking_point_id} from Part {part_id}")
            return jsonify({'status': 'success', 'message': 'Marking point deleted.'})

        except Exception as e:
            db.session.rollback()
            error_logger.exception(f"Error deleting marking point {marking_point_id}: {e}")
            return jsonify({'status': 'error', 'message': 'Error deleting marking point.'}), 500


    @app.route('/add_marking_point/<int:part_id>', methods=['POST'])
    @login_required # Should be admin_required?
    def add_marking_point(part_id):
        try:
            data = request.get_json()
            part = Part.query.get_or_404(part_id)

            # Create new marking point
            marking_point = MarkingPoint(
                part_id=part_id,
                description=data.get('description', 'New marking point'),
                score=float(data.get('score', 1.0)),
                order=int(data.get('order', 0)),
                is_auto_generated=False
            )

            # Validate the marking point
            marking_point.validate()

            # Add to database
            db.session.add(marking_point)
            db.session.commit()

            return jsonify({
                'status': 'success',
                'message': 'Marking point added successfully',
                'marking_point': {
                    'id': marking_point.id,
                    'description': marking_point.description,
                    'score': marking_point.score,
                    'order': marking_point.order
                }
            })
        except Exception as e:
            db.session.rollback()
            return jsonify({
                'status': 'error',
                'message': str(e)
            }), 400



    @app.route('/move_marking_point/<int:marking_point_id>/<direction>', methods=['POST'])
    @login_required # Should be admin_required?
    def move_marking_point(marking_point_id, direction):
        """Moves a marking point up or down in order within its part."""
        # Add admin check if necessary

        if direction not in ['up', 'down']:
            return jsonify({'status': 'error', 'message': 'Invalid direction.'}), 400

        marking_point = MarkingPoint.query.get_or_404(marking_point_id)
        part_id = marking_point.part_id

        try:
            # Get all marking points for this part, ordered correctly
            siblings = MarkingPoint.query.filter_by(part_id=part_id)\
                                      .order_by(MarkingPoint.order).all()

            try:
                current_index = siblings.index(marking_point)
            except ValueError:
                 # Should not happen if MP exists and belongs to the part
                 error_logger.error(f"Marking point {marking_point_id} not found in siblings list for Part {part_id}")
                 return jsonify({'status': 'error', 'message': 'Marking point consistency error.'}), 500

            if direction == 'up' and current_index > 0:
                # Swap order with the previous sibling
                prev_sibling = siblings[current_index - 1]
                marking_point.order, prev_sibling.order = prev_sibling.order, marking_point.order
                db.session.commit()
                app_logger.info(f"Moved marking point {marking_point_id} up")
                return jsonify({'status': 'success', 'message': 'Moved up.'})
            elif direction == 'down' and current_index < len(siblings) - 1:
                # Swap order with the next sibling
                next_sibling = siblings[current_index + 1]
                marking_point.order, next_sibling.order = next_sibling.order, marking_point.order
                db.session.commit()
                app_logger.info(f"Moved marking point {marking_point_id} down")
                return jsonify({'status': 'success', 'message': 'Moved down.'})
            else:
                # Cannot move further in this direction
                return jsonify({'status': 'info', 'message': 'Cannot move further.'})

        except Exception as e:
            db.session.rollback()
            error_logger.exception(f"Error moving marking point {marking_point_id}: {e}")
            return jsonify({'status': 'error', 'message': 'Error moving marking point.'}), 500

    # --- Other API Endpoints ---

    @app.route("/explain_answer/<int:question_id>/<int:part_id>")
    @login_required
    @limiter.limit("10/minute")
    def explain_answer(question_id, part_id):
        """
        Explains the answer for a specific question part using Kimi-K2 LLM.
        Returns a streaming response with the explanation.
        """
        if 'user_id' not in session:
            return jsonify({
                'status': 'error',
                'message': 'Please login to get answer explanations'
            }), 401

        update_user_activity(session['user_id'])

        try:
            # Get the part data
            part_data = Part.query.get_or_404(part_id)
            question_data = Question.query.get_or_404(question_id)

            # Initialize Pinecone for context retrieval
            pc = Pinecone(api_key=os.getenv("PINECONE_API_KEY"))
            index = pc.Index(host=os.getenv("PINECONE_INDEX_CHEM"))

            # Query Pinecone for relevant context
            context_text = ""
            try:
                # Create query text from question and topic context
                query_text = f"{part_data.description}"
                if part_data.answer:
                    query_text += f" {part_data.answer}"
                if part_data.question.topic:
                    query_text += f" {part_data.question.topic.name}"
                if part_data.question.topic.subject:
                    query_text += f" {part_data.question.topic.subject.name}"

                # Query Pinecone index for relevant context
                query_payload = {
                    "inputs": {
                        "text": query_text
                    },
                    "top_k": 5  # Get more context for explanations
                }

                query_response = index.search(
                    namespace="__default__",
                    query=query_payload
                )

                for item in query_response['result']['hits']:
                    context_text += f"{item['fields']['title']}: {item['fields']['content']}\n"

            except Exception as e_pinecone:
                app_logger.warning(f"Pinecone query failed for explanation {part_id}: {str(e_pinecone)}")
                context_text = ""  # Continue without context if Pinecone fails

            # Construct the prompt
            if part_data.input_type == 'mcq':
                # For MCQ, include the options and correct answer
                options_text = ""
                correct_option = None

                for i, option in enumerate(part_data.options):
                    options_text += f"Option {i+1}: {option.description}\n"
                    if option.is_correct:
                        correct_option = i+1

                prompt = f"""
                You are an expert educational assistant. Explain the following multiple-choice question and why the correct answer is the best choice.

                QUESTION: {part_data.description}

                OPTIONS:
                {options_text}

                CORRECT ANSWER: Option {correct_option}

                CONTEXT (use this to enhance your explanation):
                {context_text}

                Provide a VERY CONCISE explanation (maximum 150 words) of why this is the correct answer. Focus on the key concepts and principles. Use the context above to provide more detailed and accurate explanations.

                IMPORTANT FORMATTING RULES:
                - Use ONLY plain text with headings marked by # symbols
                - DO NOT use markdown formatting like **bold**, *italic*, or `code`
                - DO NOT use markdown lists with - or * symbols
                - Use numbered lists (1., 2., 3.) or bullet points with • symbol only
                - Use LaTeX for mathematical expressions: $...$ for inline and $$...$$ for display math
                - Keep text clean and simple for web display

                Format your response using this structure:
                # Key Concept
                Brief explanation of the main concept being tested

                # Why Option {correct_option} is Correct
                Concise explanation with clear reasoning

                """
            else:
                # For text/SAQ questions
                prompt = f"""
                You are an expert educational assistant. Explain the following question and its answer.

                QUESTION: {part_data.description}

                MODEL ANSWER: {part_data.answer}

                CONTEXT (use this to enhance your explanation):
                {context_text}

                Provide a VERY CONCISE explanation (maximum 50 words) which will explain to a student who has no idea what is going

                IMPORTANT FORMATTING RULES:
                - Use ONLY plain text with headings marked by # symbols
                - DO NOT use markdown formatting like **bold**, *italic*, or `code`
                - DO NOT use markdown lists with - or * symbols
                - Use numbered lists (1., 2., 3.) or bullet points with • symbol only
                - Use LaTeX for mathematical expressions: $...$ for inline and $$...$$ for display math
                - Keep text clean and simple for web display

                
                """

            # Create a streaming response
            def generate():
                # Use Kimi-K2 if available, otherwise fall back to Gemini
                if is_kimi_k2_available():
                    try:
                        # Use Kimi-K2 through Groq (non-streaming)
                        completion = groq_client.chat.completions.create(
                            model="moonshotai/kimi-k2-instruct",
                            messages=[
                                {"role": "system", "content": "You are an expert educational assistant providing concise explanations for chemistry questions."},
                                {"role": "user", "content": prompt}
                            ],
                            temperature=0.1,
                        )

                        response_text = completion.choices[0].message.content
                        app_logger.info(f"Using Kimi-K2 for answer explanation - Question ID: {question_id}, Part ID: {part_id}")
                        yield response_text

                    except Exception as e:
                        app_logger.warning(f"Kimi-K2 failed for explanation, falling back to Gemini: {str(e)}")
                        app_logger.info(f"Falling back to Gemini for answer explanation - Question ID: {question_id}, Part ID: {part_id}")
                        # Fall back to Gemini
                        generation_config = {
                            "temperature": 0.1,
                        }

                        safety_settings = [
                            {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_NONE"},
                            {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_NONE"},
                            {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_NONE"},
                            {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_NONE"}
                        ]

                        response = gemini_grading_client.generate_content(
                            prompt,
                            generation_config=generation_config,
                            safety_settings=safety_settings,
                            stream=True
                        )

                        for chunk in response:
                            if chunk.text:
                                yield chunk.text
                else:
                    # Use Gemini as fallback
                    app_logger.info(f"Using Gemini for answer explanation (Kimi-K2 not available) - Question ID: {question_id}, Part ID: {part_id}")
                    generation_config = {
                        "temperature": 0.1,
                    }

                    safety_settings = [
                        {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_NONE"},
                        {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_NONE"},
                        {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_NONE"},
                        {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_NONE"}
                    ]

                    response = gemini_grading_client.generate_content(
                        prompt,
                        generation_config=generation_config,
                        safety_settings=safety_settings,
                        stream=True
                    )

                    for chunk in response:
                        if chunk.text:
                            yield chunk.text

            return app.response_class(generate(), mimetype='text/plain')

        except Exception as e:
            error_logger.exception(f"Error generating answer explanation: {str(e)}")
            return jsonify({
                'status': 'error',
                'message': 'An error occurred while generating the explanation'
            }), 500

    @app.route("/get_activity_data/<int:user_id>")
    @login_required # Or admin_required? Check permissions needed.
    def get_activity_data(user_id):
        """Retrieves daily activity counts for a user."""
        # Security check: Ensure the requesting user has permission to view this data
        # e.g., is the user themselves or an admin.
        requesting_user_id = session['user_id']
        requesting_user = User.query.get(requesting_user_id)
        if user_id != requesting_user_id and (not requesting_user or requesting_user.role != 'admin'):
             return jsonify({'status': 'error', 'message': 'Permission denied.'}), 403

        try:
            # Query activities for the specified user
            activities = DailyActivity.query.filter_by(user_id=user_id).all()
            # Format data as date string -> count dictionary
            activity_data = {activity.date.isoformat(): activity.activity_count for activity in activities}

            return jsonify(activity_data)
        except Exception as e:
             error_logger.exception(f"Error fetching activity data for user {user_id}: {e}")
             return jsonify({'status': 'error', 'message': 'Could not retrieve activity data.'}), 500

    @app.route("/get_daily_time_data/<int:user_id>")
    @login_required
    def get_daily_time_data(user_id):
        """Retrieves daily active time data for a user."""
        # Security check: Ensure the requesting user has permission to view this data
        requesting_user_id = session['user_id']
        requesting_user = User.query.get(requesting_user_id)
        if user_id != requesting_user_id and (not requesting_user or requesting_user.role != 'admin'):
             return jsonify({'status': 'error', 'message': 'Permission denied.'}), 403

        try:
            # Query daily active time for the specified user
            daily_times = DailyActiveTime.query.filter_by(user_id=user_id).all()
            # Format data as date string -> seconds dictionary
            time_data = {time_entry.date.isoformat(): time_entry.active_time for time_entry in daily_times}

            return jsonify(time_data)
        except Exception as e:
             error_logger.exception(f"Error fetching daily time data for user {user_id}: {e}")
             return jsonify({'status': 'error', 'message': 'Could not retrieve daily time data.'}), 500

    @app.route("/update_active_time", methods=['POST'])
    @login_required
    def update_active_time():
        """Updates the active time for the current user."""
        if 'user_id' not in session:
            return jsonify({'status': 'error', 'message': 'Not logged in'}), 401

        user_id = session['user_id']
        data = request.get_json()

        if not data or 'seconds' not in data:
            return jsonify({'status': 'error', 'message': 'Missing seconds parameter'}), 400

        try:
            seconds = int(data['seconds'])
            if seconds <= 0:
                return jsonify({'status': 'error', 'message': 'Seconds must be positive'}), 400

            # Get today's date
            today = datetime.now().date()

            # Get or create the DailyActiveTime record for today
            daily_active_time = DailyActiveTime.query.filter_by(
                user_id=user_id,
                date=today
            ).first()

            if not daily_active_time:
                daily_active_time = DailyActiveTime(
                    user_id=user_id,
                    date=today,
                    active_time=0
                )
                db.session.add(daily_active_time)

            # Update the active time
            daily_active_time.active_time += seconds

            # Update the user's last_active timestamp
            user = User.query.get(user_id)
            if user:
                user.last_active = datetime.now()

            db.session.commit()

            # Trigger feed generation for the user (but only occasionally to avoid spam)
            # Only trigger if the user has accumulated significant time (every 30 minutes)
            if daily_active_time.active_time % 1800 < seconds:  # Every 30 minutes
                try:
                    from routes.feed_utils import trigger_feed_generation_for_user
                    trigger_feed_generation_for_user(user_id)
                except Exception as e:
                    app_logger.warning(f"Failed to generate feed posts for user {user_id}: {e}")

            # Return the updated active time
            return jsonify({
                'status': 'success',
                'active_time': daily_active_time.active_time
            })

        except ValueError:
            return jsonify({'status': 'error', 'message': 'Invalid seconds value'}), 400
        except Exception as e:
            db.session.rollback()
            error_logger.exception(f"Error updating active time for user {user_id}: {e}")
            return jsonify({'status': 'error', 'message': 'Could not update active time'}), 500

    @app.route("/get_active_time", methods=['GET'])
    @login_required
    def get_active_time():
        """Gets the total active time for the current user."""
        if 'user_id' not in session:
            return jsonify({'status': 'error', 'message': 'Not logged in'}), 401

        user_id = session['user_id']

        try:
            # Get today's date
            today = datetime.now().date()

            # Get the DailyActiveTime record for today
            daily_active_time = DailyActiveTime.query.filter_by(
                user_id=user_id,
                date=today
            ).first()

            today_time = daily_active_time.active_time if daily_active_time else 0

            # Get total active time across all days
            from sqlalchemy import func
            total_time_result = db.session.query(func.sum(DailyActiveTime.active_time)).filter(
                DailyActiveTime.user_id == user_id
            ).first()

            total_time = total_time_result[0] if total_time_result[0] else 0

            # Get the user's daily time goal
            user = User.query.get(user_id)
            daily_goal = user.daily_time_goal if user and user.daily_time_goal else 3600  # Default: 1 hour

            # Calculate progress percentage
            progress_percent = min(round((today_time / daily_goal) * 100), 100) if daily_goal > 0 else 0

            return jsonify({
                'status': 'success',
                'today_time': today_time,
                'total_time': total_time,
                'daily_goal': daily_goal,
                'progress_percent': progress_percent
            })

        except Exception as e:
            error_logger.exception(f"Error getting active time for user {user_id}: {e}")
            return jsonify({'status': 'error', 'message': 'Could not retrieve active time'}), 500

    @app.route("/set_time_goal", methods=['POST'])
    @login_required
    def set_time_goal():
        """Sets the daily time goal for the current user."""
        if 'user_id' not in session:
            return jsonify({'status': 'error', 'message': 'Not logged in'}), 401

        user_id = session['user_id']
        data = request.get_json()

        if not data or 'goal_minutes' not in data:
            return jsonify({'status': 'error', 'message': 'Missing goal_minutes parameter'}), 400

        try:
            goal_minutes = int(data['goal_minutes'])
            if goal_minutes <= 0:
                return jsonify({'status': 'error', 'message': 'Goal minutes must be positive'}), 400

            # Convert minutes to seconds
            goal_seconds = goal_minutes * 60

            # Update the user's daily time goal
            user = User.query.get(user_id)
            if not user:
                return jsonify({'status': 'error', 'message': 'User not found'}), 404

            user.daily_time_goal = goal_seconds
            db.session.commit()

            # Get today's active time for progress calculation
            today = datetime.now().date()
            daily_active_time = DailyActiveTime.query.filter_by(
                user_id=user_id,
                date=today
            ).first()

            today_time = daily_active_time.active_time if daily_active_time else 0

            # Calculate progress percentage
            progress_percent = min(round((today_time / goal_seconds) * 100), 100) if goal_seconds > 0 else 0

            return jsonify({
                'status': 'success',
                'daily_goal': goal_seconds,
                'goal_minutes': goal_minutes,
                'progress_percent': progress_percent
            })

        except ValueError:
            return jsonify({'status': 'error', 'message': 'Invalid goal_minutes value'}), 400
        except Exception as e:
            db.session.rollback()
            error_logger.exception(f"Error setting time goal for user {user_id}: {e}")
            return jsonify({'status': 'error', 'message': 'Could not set time goal'}), 500

    @app.route('/notes')
    @login_required
    def notes():
        """Display chemistry notes chapter list"""
        try:
            # Get the chemistry_notes_markdown directory path
            notes_dir = os.path.join(os.getcwd(), 'chemistry_notes_markdown')

            if not os.path.exists(notes_dir):
                flash('Chemistry notes directory not found.', 'error')
                return redirect(url_for('index'))

            # Get all markdown files
            markdown_files = []
            for filename in os.listdir(notes_dir):
                if filename.endswith('.md'):
                    markdown_files.append({
                        'filename': filename,
                        'title': filename.replace('.md', '').replace('_', ' '),
                        'chapter_id': filename.replace('.md', '')
                    })

            # Sort files by filename for consistent ordering
            markdown_files.sort(key=lambda x: x['filename'])

            return render_template('notes.html', chapters=markdown_files)

        except Exception as e:
            error_logger.exception(f"Error loading chemistry notes: {e}")
            flash('Error loading chemistry notes.', 'error')
            return redirect(url_for('index'))

    @app.route('/notes/<path:chapter_id>')
    @login_required
    def view_chapter(chapter_id):
        """Display a specific chemistry notes chapter"""
        try:
            # URL decode the chapter_id to handle spaces and special characters
            from urllib.parse import unquote
            chapter_id = unquote(chapter_id)

            # Get the chemistry_notes_markdown directory path
            notes_dir = os.path.join(os.getcwd(), 'chemistry_notes_markdown')

            if not os.path.exists(notes_dir):
                flash('Chemistry notes directory not found.', 'error')
                return redirect(url_for('notes'))

            # Construct the file path
            filename = f"{chapter_id}.md"
            file_path = os.path.join(notes_dir, filename)

            # Security check - ensure the file exists and is within the notes directory
            if not os.path.exists(file_path) or not os.path.abspath(file_path).startswith(os.path.abspath(notes_dir)):
                flash('Chapter not found.', 'error')
                return redirect(url_for('notes'))

            # Read and process the markdown file
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # BETTER APPROACH: Use unique, safe placeholders that won't be modified by markdown
                import uuid
                latex_map = {}

                def create_safe_placeholder(match):
                    # Use UUID to ensure uniqueness and avoid conflicts
                    placeholder_id = str(uuid.uuid4()).replace('-', '')
                    safe_placeholder = f"LATEXSAFE{placeholder_id}LATEXSAFE"
                    latex_map[safe_placeholder] = match.group(0)
                    return safe_placeholder

                # Protect LaTeX with safe placeholders BEFORE markdown processing
                # Process display math first ($$...$$)
                content = re.sub(r'\$\$([^$]+?)\$\$', create_safe_placeholder, content, flags=re.DOTALL)
                # Process inline math ($...$)
                content = re.sub(r'\$([^$\n]+?)\$', create_safe_placeholder, content)



                # Configure markdown with table support and TOC
                md = markdown.Markdown(extensions=[
                    'tables',  # Enable table support
                    'fenced_code',
                    'codehilite',
                    'toc'
                ], extension_configs={
                    'tables': {
                        'use_align_attribute': True
                    },
                    'toc': {
                        'permalink': True,
                        'permalink_class': 'section-link',
                        'permalink_title': 'Link to this section',
                        'baselevel': 1,
                        'slugify': lambda value, separator: re.sub(r'[-\s]+', separator, re.sub(r'[^\w\s-]', '', value.replace('°', 'circ')).strip().lower())
                    }
                }, output_format='html')

                # Convert markdown to HTML (placeholders should be safe from modification)
                html_content = md.convert(content)

                # Extract table of contents
                toc_html = md.toc if hasattr(md, 'toc') else ''

                # Get TOC sections from database chunks instead of parsing markdown
                toc_sections = []
                try:
                    # Query database chunks for this chapter, ordered by start_line to maintain document order
                    chunks = NotesChunk.query.filter_by(chapter_id=chapter_id).order_by(NotesChunk.start_line).all()

                    for chunk in chunks:
                        toc_sections.append({
                            'id': chunk.section_id,
                            'title': chunk.title,
                            'level': chunk.level
                        })

                    app_logger.info(f"Loaded {len(toc_sections)} TOC sections from database for chapter {chapter_id}")

                except Exception as e:
                    app_logger.warning(f"Failed to load TOC sections from database for chapter {chapter_id}: {e}")
                    # Fallback to parsing TOC HTML if database query fails
                    if toc_html:
                        from html import unescape
                        from bs4 import BeautifulSoup

                        # Parse the TOC HTML to extract hierarchical structure
                        soup = BeautifulSoup(toc_html, 'html.parser')

                        def extract_toc_items(element, level=1):
                            items = []
                            for li in element.find_all('li', recursive=False):
                                link = li.find('a')
                                if link:
                                    section_id = link.get('href', '').lstrip('#')
                                    section_title = unescape(link.get_text().strip())
                                    items.append({
                                        'id': section_id,
                                        'title': section_title,
                                        'level': level
                                    })

                                    # Check for nested lists (sub-sections)
                                    nested_ul = li.find('ul')
                                    if nested_ul:
                                        items.extend(extract_toc_items(nested_ul, level + 1))
                            return items

                        # Find the main TOC list
                        main_list = soup.find('ul')
                        if main_list:
                            toc_sections = extract_toc_items(main_list)

                # Restore LaTeX from safe placeholders
                for placeholder, original_latex in latex_map.items():
                    html_content = html_content.replace(placeholder, original_latex)

                # Now process LaTeX in the HTML content using a more robust approach
                def process_latex_in_html(html_text):
                    # Process display math $$...$$ first (to avoid conflicts with inline math)
                    html_text = re.sub(
                        r'\$\$([^$]+?)\$\$',
                        lambda m: f'<span class="katex-display-math" data-latex="{m.group(1).strip()}"></span>',
                        html_text,
                        flags=re.DOTALL
                    )

                    # Process inline math $...$
                    html_text = re.sub(
                        r'\$([^$\n]+?)\$',
                        lambda m: f'<span class="katex-inline-math" data-latex="{m.group(1).strip()}"></span>',
                        html_text
                    )

                    return html_text

                # Apply LaTeX processing to the HTML
                html_content = process_latex_in_html(html_content)

                # Post-process to fix HTML tags that should be rendered
                def fix_html_tags(html_text):
                    # Fix common HTML tags that might be escaped or not processed correctly
                    # Handle <em> tags for italics
                    html_text = re.sub(r'&lt;em&gt;(.*?)&lt;/em&gt;', r'<em>\1</em>', html_text)
                    html_text = re.sub(r'&lt;i&gt;(.*?)&lt;/i&gt;', r'<i>\1</i>', html_text)
                    html_text = re.sub(r'&lt;strong&gt;(.*?)&lt;/strong&gt;', r'<strong>\1</strong>', html_text)
                    html_text = re.sub(r'&lt;b&gt;(.*?)&lt;/b&gt;', r'<b>\1</b>', html_text)
                    html_text = re.sub(r'&lt;u&gt;(.*?)&lt;/u&gt;', r'<u>\1</u>', html_text)
                    html_text = re.sub(r'&lt;sup&gt;(.*?)&lt;/sup&gt;', r'<sup>\1</sup>', html_text)
                    html_text = re.sub(r'&lt;sub&gt;(.*?)&lt;/sub&gt;', r'<sub>\1</sub>', html_text)
                    html_text = re.sub(r'&lt;br&gt;', r'<br>', html_text)
                    html_text = re.sub(r'&lt;br/&gt;', r'<br/>', html_text)
                    html_text = re.sub(r'&lt;br /&gt;', r'<br />', html_text)

                    return html_text

                # Apply HTML tag fixes
                html_content = fix_html_tags(html_content)

                # Process image paths to use the correct route
                # Replace relative image paths with the proper route
                html_content = re.sub(
                    r'<img([^>]*?)src="images/([^"]+)"([^>]*?)>',
                    r'<img\1src="/chemistry_notes_images/\2"\3>',
                    html_content
                )

                chapter_info = {
                    'title': chapter_id.replace('_', ' '),
                    'filename': filename,
                    'html': html_content,
                    'chapter_id': chapter_id,
                    'toc_sections': toc_sections
                }

                return render_template('chapter.html', chapter=chapter_info)

            except Exception as e:
                error_logger.exception(f"Error processing markdown file {filename}: {e}")
                flash('Error processing chapter content.', 'error')
                return redirect(url_for('notes'))

        except Exception as e:
            error_logger.exception(f"Error loading chapter {chapter_id}: {e}")
            flash('Error loading chapter.', 'error')
            return redirect(url_for('notes'))

    @app.route('/notes/<path:chapter_id>/<section_id>')
    @login_required
    def view_section(chapter_id, section_id):
        """Display a specific section within a chemistry notes chapter"""
        try:
            # URL decode the chapter_id to handle spaces and special characters
            from urllib.parse import unquote
            chapter_id = unquote(chapter_id)
            section_id = unquote(section_id)

            # Get the chemistry_notes_markdown directory path
            notes_dir = os.path.join(os.getcwd(), 'chemistry_notes_markdown')

            if not os.path.exists(notes_dir):
                flash('Chemistry notes directory not found.', 'error')
                return redirect(url_for('notes'))

            # Construct the file path
            filename = f"{chapter_id}.md"
            file_path = os.path.join(notes_dir, filename)

            # Security check - ensure the file exists and is within the notes directory
            if not os.path.exists(file_path) or not os.path.abspath(file_path).startswith(os.path.abspath(notes_dir)):
                flash('Chapter not found.', 'error')
                return redirect(url_for('notes'))

            # Read and process the markdown file (same as view_chapter)
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # Process the markdown the same way as view_chapter
                import uuid
                latex_map = {}

                def create_safe_placeholder(match):
                    placeholder_id = str(uuid.uuid4()).replace('-', '')
                    safe_placeholder = f"LATEXSAFE{placeholder_id}LATEXSAFE"
                    latex_map[safe_placeholder] = match.group(0)
                    return safe_placeholder

                # Protect LaTeX with safe placeholders BEFORE markdown processing
                content = re.sub(r'\$\$([^$]+?)\$\$', create_safe_placeholder, content, flags=re.DOTALL)
                content = re.sub(r'\$([^$\n]+?)\$', create_safe_placeholder, content)

                # Configure markdown with table support and TOC
                md = markdown.Markdown(extensions=[
                    'tables',
                    'fenced_code',
                    'codehilite',
                    'toc'
                ], extension_configs={
                    'tables': {
                        'use_align_attribute': True
                    },
                    'toc': {
                        'permalink': True,
                        'permalink_class': 'section-link',
                        'permalink_title': 'Link to this section',
                        'baselevel': 1,
                        'slugify': lambda value, separator: re.sub(r'[-\s]+', separator, re.sub(r'[^\w\s-]', '', value.replace('°', 'circ')).strip().lower())
                    }
                }, output_format='html')

                # Convert markdown to HTML
                html_content = md.convert(content)

                # Extract table of contents
                toc_html = md.toc if hasattr(md, 'toc') else ''

                # Get TOC sections from database chunks instead of parsing markdown
                toc_sections = []
                try:
                    # Query database chunks for this chapter, ordered by start_line to maintain document order
                    chunks = NotesChunk.query.filter_by(chapter_id=chapter_id).order_by(NotesChunk.start_line).all()

                    for chunk in chunks:
                        toc_sections.append({
                            'id': chunk.section_id,
                            'title': chunk.title,
                            'level': chunk.level
                        })

                    app_logger.info(f"Loaded {len(toc_sections)} TOC sections from database for chapter {chapter_id} (view_section)")

                except Exception as e:
                    app_logger.warning(f"Failed to load TOC sections from database for chapter {chapter_id} (view_section): {e}")
                    # Fallback to parsing TOC HTML if database query fails
                    if toc_html:
                        from html import unescape

                        soup = BeautifulSoup(toc_html, 'html.parser')

                        def extract_toc_items(element, level=1):
                            items = []
                            for li in element.find_all('li', recursive=False):
                                link = li.find('a')
                                if link:
                                    section_id_found = link.get('href', '').lstrip('#')
                                    section_title = unescape(link.get_text().strip())
                                    items.append({
                                        'id': section_id_found,
                                        'title': section_title,
                                        'level': level
                                    })

                                    nested_ul = li.find('ul')
                                    if nested_ul:
                                        items.extend(extract_toc_items(nested_ul, level + 1))
                            return items

                        main_list = soup.find('ul')
                        if main_list:
                            toc_sections = extract_toc_items(main_list)

                # Restore LaTeX from safe placeholders
                for placeholder, original_latex in latex_map.items():
                    html_content = html_content.replace(placeholder, original_latex)

                # Process LaTeX in HTML
                def process_latex_in_html(html_text):
                    html_text = re.sub(
                        r'\$\$([^$]+?)\$\$',
                        lambda m: f'<span class="katex-display-math" data-latex="{m.group(1).strip()}"></span>',
                        html_text,
                        flags=re.DOTALL
                    )
                    html_text = re.sub(
                        r'\$([^$\n]+?)\$',
                        lambda m: f'<span class="katex-inline-math" data-latex="{m.group(1).strip()}"></span>',
                        html_text
                    )
                    return html_text

                html_content = process_latex_in_html(html_content)

                # Fix HTML tags
                def fix_html_tags(html_text):
                    html_text = re.sub(r'&lt;em&gt;(.*?)&lt;/em&gt;', r'<em>\1</em>', html_text)
                    html_text = re.sub(r'&lt;i&gt;(.*?)&lt;/i&gt;', r'<i>\1</i>', html_text)
                    html_text = re.sub(r'&lt;strong&gt;(.*?)&lt;/strong&gt;', r'<strong>\1</strong>', html_text)
                    html_text = re.sub(r'&lt;b&gt;(.*?)&lt;/b&gt;', r'<b>\1</b>', html_text)
                    html_text = re.sub(r'&lt;u&gt;(.*?)&lt;/u&gt;', r'<u>\1</u>', html_text)
                    html_text = re.sub(r'&lt;sup&gt;(.*?)&lt;/sup&gt;', r'<sup>\1</sup>', html_text)
                    html_text = re.sub(r'&lt;sub&gt;(.*?)&lt;/sub&gt;', r'<sub>\1</sub>', html_text)
                    html_text = re.sub(r'&lt;br&gt;', r'<br>', html_text)
                    html_text = re.sub(r'&lt;br/&gt;', r'<br/>', html_text)
                    html_text = re.sub(r'&lt;br /&gt;', r'<br />', html_text)
                    return html_text

                html_content = fix_html_tags(html_content)

                # Process image paths
                html_content = re.sub(
                    r'<img([^>]*?)src="images/([^"]+)"([^>]*?)>',
                    r'<img\1src="/chemistry_notes_images/\2"\3>',
                    html_content
                )

                chapter_info = {
                    'title': chapter_id.replace('_', ' '),
                    'filename': filename,
                    'html': html_content,
                    'chapter_id': chapter_id,
                    'toc_sections': toc_sections,
                    'target_section': section_id  # Add target section for auto-scroll
                }

                return render_template('chapter.html', chapter=chapter_info)

            except Exception as e:
                error_logger.exception(f"Error processing markdown file {filename}: {e}")
                flash('Error processing chapter content.', 'error')
                return redirect(url_for('notes'))

        except Exception as e:
            error_logger.exception(f"Error loading section {section_id} in chapter {chapter_id}: {e}")
            flash('Error loading section.', 'error')
            return redirect(url_for('notes'))

    @app.route('/api/notes/sections')
    @login_required
    def get_notes_sections():
        """API endpoint to get all available sections across all chemistry notes for RAG"""
        try:
            # Get sections from database chunks instead of parsing markdown files
            all_sections = []

            try:
                # Query all chunks, ordered by chapter_id and start_line to maintain document order
                chunks = NotesChunk.query.order_by(NotesChunk.chapter_id, NotesChunk.start_line).all()

                for chunk in chunks:
                    all_sections.append({
                        'chapter_id': chunk.chapter_id,
                        'chapter_title': chunk.chapter_id.replace('_', ' '),
                        'section_id': chunk.section_id,
                        'section_title': chunk.title,
                        'level': chunk.level,
                        'url': f"/notes/{chunk.chapter_id}#{chunk.section_id}",
                        'direct_url': f"/notes/{chunk.chapter_id}/{chunk.section_id}"
                    })

                app_logger.info(f"Retrieved {len(all_sections)} sections from database chunks")

            except Exception as e:
                app_logger.warning(f"Failed to load sections from database chunks: {e}")
                # Fallback to parsing markdown files if database query fails
                notes_dir = os.path.join(os.getcwd(), 'chemistry_notes_markdown')

                if not os.path.exists(notes_dir):
                    return jsonify({'error': 'Chemistry notes directory not found and database chunks unavailable.'}), 404

                # Get all markdown files
                for filename in os.listdir(notes_dir):
                    if filename.endswith('.md'):
                        chapter_id = filename.replace('.md', '')
                        file_path = os.path.join(notes_dir, filename)

                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read()

                            # Process markdown to extract TOC
                            import uuid
                            latex_map = {}

                            def create_safe_placeholder(match):
                                placeholder_id = str(uuid.uuid4()).replace('-', '')
                                safe_placeholder = f"LATEXSAFE{placeholder_id}LATEXSAFE"
                                latex_map[safe_placeholder] = match.group(0)
                                return safe_placeholder

                            # Protect LaTeX
                            content = re.sub(r'\$\$([^$]+?)\$\$', create_safe_placeholder, content, flags=re.DOTALL)
                            content = re.sub(r'\$([^$\n]+?)\$', create_safe_placeholder, content)

                            # Configure markdown with TOC
                            md = markdown.Markdown(extensions=[
                                'tables',
                                'fenced_code',
                                'codehilite',
                                'toc'
                            ], extension_configs={
                                'toc': {
                                    'permalink': True,
                                    'permalink_class': 'section-link',
                                    'permalink_title': 'Link to this section',
                                    'baselevel': 1,
                                    'slugify': lambda value, separator: re.sub(r'[-\s]+', separator, re.sub(r'[^\w\s-]', '', value.replace('°', 'circ')).strip().lower())
                                }
                            }, output_format='html')

                            # Convert markdown to HTML
                            html_content = md.convert(content)

                            # Extract table of contents
                            toc_html = md.toc if hasattr(md, 'toc') else ''

                            # Parse TOC to extract section data
                            if toc_html:
                                from html import unescape

                                soup = BeautifulSoup(toc_html, 'html.parser')

                                def extract_toc_items(element, level=1):
                                    items = []
                                    for li in element.find_all('li', recursive=False):
                                        link = li.find('a')
                                        if link:
                                            section_id_found = link.get('href', '').lstrip('#')
                                            section_title = unescape(link.get_text().strip())
                                            items.append({
                                                'chapter_id': chapter_id,
                                                'chapter_title': chapter_id.replace('_', ' '),
                                                'section_id': section_id_found,
                                                'section_title': section_title,
                                                'level': level,
                                                'url': f"/notes/{chapter_id}#{section_id_found}",
                                                'direct_url': f"/notes/{chapter_id}/{section_id_found}"
                                            })

                                            nested_ul = li.find('ul')
                                            if nested_ul:
                                                items.extend(extract_toc_items(nested_ul, level + 1))
                                    return items

                                main_list = soup.find('ul')
                                if main_list:
                                    chapter_sections = extract_toc_items(main_list)
                                    all_sections.extend(chapter_sections)

                        except Exception as e:
                            error_logger.exception(f"Error processing {filename}: {e}")
                            continue

            return jsonify({
                'sections': all_sections,
                'total_sections': len(all_sections),
                'message': 'Successfully retrieved all chemistry notes sections'
            })

        except Exception as e:
            error_logger.exception(f"Error getting notes sections: {e}")
            return jsonify({'error': 'Internal server error'}), 500

    @app.route('/api/notes/<path:chapter_id>/<section_id>')
    @login_required
    def get_section_content(chapter_id, section_id):
        """API endpoint to get specific section content for RAG"""
        try:
            # URL decode the chapter_id to handle spaces and special characters
            from urllib.parse import unquote
            chapter_id = unquote(chapter_id)
            section_id = unquote(section_id)

            # First try to get content from database chunks
            try:
                chunk = NotesChunk.query.filter_by(
                    chapter_id=chapter_id,
                    section_id=section_id
                ).first()

                if chunk:
                    app_logger.info(f"Retrieved section content from database chunk for {chapter_id}/{section_id}")
                    return jsonify({
                        'chapter_id': chunk.chapter_id,
                        'chapter_title': chunk.chapter_id.replace('_', ' '),
                        'section_id': chunk.section_id,
                        'section_title': chunk.title,
                        'content_html': chunk.content,  # Chunk content is already processed
                        'content_text': BeautifulSoup(chunk.content, 'html.parser').get_text(),
                        'url': f"/notes/{chunk.chapter_id}#{chunk.section_id}",
                        'direct_url': f"/notes/{chunk.chapter_id}/{chunk.section_id}",
                        'level': chunk.level,
                        'word_count': chunk.word_count,
                        'char_count': chunk.char_count
                    })
                else:
                    app_logger.warning(f"Section {section_id} not found in database chunks for chapter {chapter_id}")

            except Exception as e:
                app_logger.warning(f"Failed to retrieve section from database chunks for {chapter_id}/{section_id}: {e}")

            # Fallback to parsing markdown file if database chunk not found
            # Get the chemistry_notes_markdown directory path
            notes_dir = os.path.join(os.getcwd(), 'chemistry_notes_markdown')

            if not os.path.exists(notes_dir):
                return jsonify({'error': 'Chemistry notes directory not found and database chunk unavailable.'}), 404

            # Construct the file path
            filename = f"{chapter_id}.md"
            file_path = os.path.join(notes_dir, filename)

            # Security check
            if not os.path.exists(file_path) or not os.path.abspath(file_path).startswith(os.path.abspath(notes_dir)):
                return jsonify({'error': 'Chapter not found.'}), 404

            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # Process markdown to extract the specific section
                import uuid
                latex_map = {}

                def create_safe_placeholder(match):
                    placeholder_id = str(uuid.uuid4()).replace('-', '')
                    safe_placeholder = f"LATEXSAFE{placeholder_id}LATEXSAFE"
                    latex_map[safe_placeholder] = match.group(0)
                    return safe_placeholder

                # Protect LaTeX
                content = re.sub(r'\$\$([^$]+?)\$\$', create_safe_placeholder, content, flags=re.DOTALL)
                content = re.sub(r'\$([^$\n]+?)\$', create_safe_placeholder, content)

                # Configure markdown
                md = markdown.Markdown(extensions=[
                    'tables',
                    'fenced_code',
                    'codehilite',
                    'toc'
                ], extension_configs={
                    'toc': {
                        'permalink': True,
                        'permalink_class': 'section-link',
                        'permalink_title': 'Link to this section',
                        'baselevel': 1,
                        'slugify': lambda value, separator: re.sub(r'[-\s]+', separator, re.sub(r'[^\w\s-]', '', value.replace('°', 'circ')).strip().lower())
                    }
                }, output_format='html')

                # Convert markdown to HTML
                html_content = md.convert(content)

                # Restore LaTeX
                for placeholder, original_latex in latex_map.items():
                    html_content = html_content.replace(placeholder, original_latex)

                # Extract the specific section content
                from bs4 import BeautifulSoup
                soup = BeautifulSoup(html_content, 'html.parser')

                # Find the target section
                target_element = soup.find(id=section_id)
                if not target_element:
                    return jsonify({'error': f'Section {section_id} not found.'}), 404

                # Extract content from this section until the next heading of same or higher level
                section_content = []
                current_element = target_element
                target_level = int(target_element.name[1]) if target_element.name.startswith('h') else 1

                # Add the heading itself
                section_content.append(str(current_element))

                # Get all following siblings until next heading of same or higher level
                for sibling in current_element.next_siblings:
                    if hasattr(sibling, 'name') and sibling.name and sibling.name.startswith('h'):
                        sibling_level = int(sibling.name[1])
                        if sibling_level <= target_level:
                            break
                    section_content.append(str(sibling))

                section_html = ''.join(section_content)

                return jsonify({
                    'chapter_id': chapter_id,
                    'chapter_title': chapter_id.replace('_', ' '),
                    'section_id': section_id,
                    'section_title': target_element.get_text().strip(),
                    'content_html': section_html,
                    'content_text': BeautifulSoup(section_html, 'html.parser').get_text(),
                    'url': f"/notes/{chapter_id}#{section_id}",
                    'direct_url': f"/notes/{chapter_id}/{section_id}"
                })

            except Exception as e:
                error_logger.exception(f"Error processing section {section_id} in {filename}: {e}")
                return jsonify({'error': 'Error processing section content.'}), 500

        except Exception as e:
            error_logger.exception(f"Error getting section content for {chapter_id}/{section_id}: {e}")
            return jsonify({'error': 'Internal server error'}), 500

    @app.route('/api/notes/search', methods=['POST'])
    @login_required
    def search_notes():
        """
        Search chemistry notes using RAG system.

        Expects JSON payload:
        {
            "query": "search query",
            "top_k": 5,  // optional, default 5
            "min_score": 0.3  // optional, default 0.3
        }
        """
        try:
            data = request.get_json()
            if not data or 'query' not in data:
                return jsonify({'error': 'Query is required'}), 400

            query = data['query'].strip()
            if not query:
                return jsonify({'error': 'Query cannot be empty'}), 400

            top_k = data.get('top_k', 5)
            min_score = data.get('min_score', 0.3)

            # Validate parameters
            if not isinstance(top_k, int) or top_k < 1 or top_k > 20:
                return jsonify({'error': 'top_k must be an integer between 1 and 20'}), 400

            if not isinstance(min_score, (int, float)) or min_score < 0 or min_score > 1:
                return jsonify({'error': 'min_score must be a number between 0 and 1'}), 400

            # Check if RAG dependencies are available
            try:
                from notes_rag_system import NotesRAGSystem, DEPENDENCIES_AVAILABLE
                if not DEPENDENCIES_AVAILABLE:
                    return jsonify({'error': 'RAG system dependencies not available'}), 503
            except ImportError:
                return jsonify({'error': 'RAG system not available'}), 503

            # Initialize and use RAG system
            rag_system = NotesRAGSystem()

            # Search for similar chunks
            results = rag_system.search_similar_chunks(query, top_k=top_k, min_score=min_score)

            # Format results for response
            formatted_results = []
            for result in results:
                chunk = result['chunk']
                formatted_result = {
                    'id': chunk['id'],
                    'title': chunk['title'],
                    'content': chunk['content'][:500] + '...' if len(chunk['content']) > 500 else chunk['content'],
                    'full_content': chunk['content'],
                    'filename': chunk['filename'],
                    'chapter_id': chunk['chapter_id'],
                    'section_id': chunk['section_id'],
                    'level': chunk['level'],
                    'parent_sections': chunk['parent_sections'],
                    'similarity_score': result['similarity_score'],
                    'relevance_type': result['relevance_type'],
                    'word_count': chunk['word_count'],
                    'char_count': chunk['char_count']
                }
                formatted_results.append(formatted_result)

            return jsonify({
                'status': 'success',
                'query': query,
                'results': formatted_results,
                'total_results': len(formatted_results),
                'parameters': {
                    'top_k': top_k,
                    'min_score': min_score
                }
            })

        except Exception as e:
            error_logger.exception(f"Error in notes search: {e}")
            return jsonify({'error': 'Internal server error during search'}), 500

    @app.route('/api/notes/chunk/<int:chunk_id>/context')
    @login_required
    def get_chunk_context(chunk_id):
        """
        Get contextual information for a specific chunk including parent and sibling sections.
        """
        try:
            # Check if RAG dependencies are available
            try:
                from notes_rag_system import NotesRAGSystem, DEPENDENCIES_AVAILABLE
                if not DEPENDENCIES_AVAILABLE:
                    return jsonify({'error': 'RAG system dependencies not available'}), 503
            except ImportError:
                return jsonify({'error': 'RAG system not available'}), 503

            # Initialize RAG system
            rag_system = NotesRAGSystem()

            # Get chunk context
            context = rag_system.get_chunk_context(chunk_id, include_siblings=True)

            if not context:
                return jsonify({'error': 'Chunk not found'}), 404

            return jsonify({
                'status': 'success',
                'context': context
            })

        except Exception as e:
            error_logger.exception(f"Error getting chunk context: {e}")
            return jsonify({'error': 'Internal server error'}), 500

    @app.route('/chemistry_notes_images/<path:filename>')
    def serve_chemistry_notes_image(filename):
        """Serve images from chemistry_notes_markdown/images directory"""
        try:
            # Basic security check
            if '..' in filename or filename.startswith('/'):
                return "Invalid path", 400

            images_dir = os.path.join(os.getcwd(), 'chemistry_notes_markdown', 'images')

            # Ensure the resolved path is still within the images directory
            safe_file_path = os.path.abspath(os.path.join(images_dir, filename))

            if not safe_file_path.startswith(images_dir) or not os.path.exists(safe_file_path):
                return "File not found", 404

            from flask import send_from_directory
            return send_from_directory(images_dir, filename)

        except Exception as e:
            error_logger.exception(f"Error serving chemistry notes image {filename}: {e}")
            return "Error serving image", 500

    @app.route('/api/get-random-tour-dojo-question', methods=['GET'])
    @login_required
    def get_random_tour_dojo_question():
        """Get a random DOJO question from core topics for the tour"""
        try:
            from models import Topic, Question, Subject
            import random

            # Core topics for the tour
            core_topics = ['energetics', 'chemical-bonding-1', 'periodic-table']

            # Get chemistry subject
            chemistry_subject = Subject.query.filter_by(name='h2-chemistry').first()
            if not chemistry_subject:
                return jsonify({'success': False, 'message': 'Chemistry subject not found'})

            # Collect all DOJO questions from core topics
            all_questions = []
            for topic_name in core_topics:
                topic = Topic.query.filter_by(name=topic_name, subject_id=chemistry_subject.id).first()
                if topic:
                    questions = Question.query.filter_by(topic_id=topic.id, is_dojo=True).all()
                    for q in questions:
                        all_questions.append({
                            'id': q.id,
                            'title': q.title,
                            'topic_name': topic.name,
                            'topic_display_name': topic.name.replace('-', ' ').title()
                        })

            if not all_questions:
                return jsonify({'success': False, 'message': 'No DOJO questions found in core topics'})

            # Select a random question
            selected_question = random.choice(all_questions)

            return jsonify({
                'success': True,
                'question': selected_question
            })

        except Exception as e:
            error_logger.exception(f"Error getting random tour dojo question: {e}")
            return jsonify({'success': False, 'message': 'Internal server error'})

    @app.route('/api/get-next-dojo-question/<int:current_question_id>', methods=['GET'])
    @login_required
    def get_next_dojo_question(current_question_id):
        """Get the next DOJO question in the same topic"""
        try:
            from models import Question

            # Get the current question
            current_question = Question.query.get(current_question_id)
            if not current_question or not current_question.is_dojo:
                return jsonify({'success': False, 'message': 'Current question not found or not a DOJO question'})

            # Get all DOJO questions in the same topic, ordered by title
            topic_questions = Question.query.filter_by(
                topic_id=current_question.topic_id,
                is_dojo=True
            ).order_by(Question.title).all()

            if len(topic_questions) <= 1:
                return jsonify({'success': False, 'message': 'No other questions in this topic'})

            # Find the current question's index
            current_index = None
            for i, q in enumerate(topic_questions):
                if q.id == current_question_id:
                    current_index = i
                    break

            if current_index is None:
                return jsonify({'success': False, 'message': 'Current question not found in topic'})

            # Get the next question (wrap around to first if at the end)
            next_index = (current_index + 1) % len(topic_questions)
            next_question = topic_questions[next_index]

            # If we wrapped around, indicate it's the first question
            is_wrapped = next_index == 0 and current_index == len(topic_questions) - 1

            return jsonify({
                'success': True,
                'current_position': current_index + 1,
                'total_questions': len(topic_questions),
                'next_question': {
                    'id': next_question.id,
                    'title': next_question.title,
                    'topic_name': current_question.topic.name,
                    'topic_display_name': current_question.topic.name.replace('-', ' ').title(),
                    'is_wrapped': is_wrapped,
                    'position': next_index + 1
                }
            })

        except Exception as e:
            error_logger.exception(f"Error getting next dojo question for {current_question_id}: {e}")
            return jsonify({'success': False, 'message': 'Internal server error'})

    @app.route('/api/get-previous-dojo-question/<int:current_question_id>', methods=['GET'])
    @login_required
    def get_previous_dojo_question(current_question_id):
        """Get the previous DOJO question in the same topic"""
        try:
            from models import Question

            # Get the current question
            current_question = Question.query.get(current_question_id)
            if not current_question or not current_question.is_dojo:
                return jsonify({'success': False, 'message': 'Current question not found or not a DOJO question'})

            # Get all DOJO questions in the same topic, ordered by title
            topic_questions = Question.query.filter_by(
                topic_id=current_question.topic_id,
                is_dojo=True
            ).order_by(Question.title).all()

            if len(topic_questions) <= 1:
                return jsonify({'success': False, 'message': 'No other questions in this topic'})

            # Find the current question's index
            current_index = None
            for i, q in enumerate(topic_questions):
                if q.id == current_question_id:
                    current_index = i
                    break

            if current_index is None:
                return jsonify({'success': False, 'message': 'Current question not found in topic'})

            # Get the previous question (wrap around to last if at the beginning)
            prev_index = (current_index - 1) % len(topic_questions)
            prev_question = topic_questions[prev_index]

            # If we wrapped around, indicate it's the last question
            is_wrapped = prev_index == len(topic_questions) - 1 and current_index == 0

            return jsonify({
                'success': True,
                'current_position': current_index + 1,
                'total_questions': len(topic_questions),
                'previous_question': {
                    'id': prev_question.id,
                    'title': prev_question.title,
                    'topic_name': current_question.topic.name,
                    'topic_display_name': current_question.topic.name.replace('-', ' ').title(),
                    'is_wrapped': is_wrapped,
                    'position': prev_index + 1
                }
            })

        except Exception as e:
            error_logger.exception(f"Error getting previous dojo question for {current_question_id}: {e}")
            return jsonify({'success': False, 'message': 'Internal server error'})

        except Exception as e:
            error_logger.exception(f"Error getting random tour DOJO question: {str(e)}")
            return jsonify({'success': False, 'message': 'Failed to get random question'})

    @app.route('/api/save_manual_adjustment', methods=['POST'])
    @login_required
    def save_manual_adjustment():
        """Save a manual grading adjustment made by an admin"""
        try:
            # Check if user is admin
            current_user = User.query.get(session['user_id'])
            if not current_user or current_user.role != 'admin':
                return jsonify({'status': 'error', 'message': 'Admin privileges required'}), 403

            data = request.get_json()
            if not data:
                return jsonify({'status': 'error', 'message': 'No data provided'}), 400

            # Validate required fields
            required_fields = ['submission_id', 'marking_point_id', 'original_score', 'adjusted_score']
            for field in required_fields:
                if field not in data:
                    return jsonify({'status': 'error', 'message': f'Missing required field: {field}'}), 400

            submission_id = int(data['submission_id'])
            marking_point_id = int(data['marking_point_id'])
            original_score = float(data['original_score'])
            adjusted_score = float(data['adjusted_score'])
            reason = data.get('reason', '').strip()

            # Validate that submission and marking point exist
            submission = Submission.query.get(submission_id)
            if not submission:
                return jsonify({'status': 'error', 'message': 'Submission not found'}), 404

            marking_point = MarkingPoint.query.get(marking_point_id)
            if not marking_point:
                return jsonify({'status': 'error', 'message': 'Marking point not found'}), 404

            # Validate adjusted score is within valid range (0, 0.5, or 1 times the marking point score)
            max_score = marking_point.score
            valid_scores = [0, max_score * 0.5, max_score]
            if adjusted_score not in valid_scores:
                return jsonify({'status': 'error', 'message': 'Invalid adjusted score'}), 400

            # Check if adjustment already exists
            existing_adjustment = ManualGradingAdjustment.query.filter_by(
                submission_id=submission_id,
                marking_point_id=marking_point_id
            ).first()

            if existing_adjustment:
                # Update existing adjustment
                existing_adjustment.original_score = original_score
                existing_adjustment.adjusted_score = adjusted_score
                existing_adjustment.teacher_id = current_user.id
                existing_adjustment.timestamp = db.func.current_timestamp()
                existing_adjustment.reason = reason
            else:
                # Create new adjustment
                adjustment = ManualGradingAdjustment(
                    submission_id=submission_id,
                    marking_point_id=marking_point_id,
                    original_score=original_score,
                    adjusted_score=adjusted_score,
                    teacher_id=current_user.id,
                    reason=reason
                )
                db.session.add(adjustment)

            # Update the submission's feedback to reflect the manual adjustment
            if submission.feedback:
                import json
                feedback_data = json.loads(submission.feedback)
                evaluated_points = feedback_data.get('evaluated_points', [])

                # Find and update the specific marking point
                for point in evaluated_points:
                    if point.get('id') == marking_point_id:
                        point['achieved_score'] = adjusted_score
                        point['manually_adjusted'] = True
                        point['adjustment_reason'] = reason
                        point['adjusted_by'] = current_user.username
                        point['adjustment_timestamp'] = datetime.now().isoformat()

                        # Update achieved/partial status based on new score
                        if adjusted_score == max_score:
                            point['achieved'] = True
                            point['partial'] = False
                        elif adjusted_score > 0:
                            point['achieved'] = False
                            point['partial'] = True
                        else:
                            point['achieved'] = False
                            point['partial'] = False
                        break

                # Recalculate total score
                total_score = sum(point.get('achieved_score', 0) for point in evaluated_points)
                feedback_data['score'] = total_score

                # Update submission
                submission.feedback = json.dumps(feedback_data)
                submission.score = total_score

            db.session.commit()

            app_logger.info(f"Manual grading adjustment saved by {current_user.username} for submission {submission_id}, marking point {marking_point_id}: {original_score} -> {adjusted_score}")

            # Create embedding for the new/updated adjustment asynchronously
            try:
                from grading_rag_system import create_embedding_for_new_adjustment

                # Get the adjustment ID (either existing or newly created)
                adjustment_id = existing_adjustment.id if existing_adjustment else adjustment.id

                # Create embedding in background (don't block the response)
                import threading
                def create_embedding_async():
                    try:
                        success = create_embedding_for_new_adjustment(adjustment_id)
                        if success:
                            app_logger.info(f"Created RAG embedding for adjustment {adjustment_id}")
                        else:
                            app_logger.warning(f"Failed to create RAG embedding for adjustment {adjustment_id}")
                    except Exception as e:
                        app_logger.error(f"Error creating RAG embedding for adjustment {adjustment_id}: {str(e)}")

                # Start background thread
                embedding_thread = threading.Thread(target=create_embedding_async)
                embedding_thread.daemon = True
                embedding_thread.start()

            except Exception as e:
                app_logger.warning(f"Could not start embedding creation for adjustment: {str(e)}")

            return jsonify({
                'status': 'success',
                'message': 'Manual adjustment saved successfully',
                'adjusted_score': adjusted_score,
                'total_score': submission.score
            })

        except ValueError as e:
            return jsonify({'status': 'error', 'message': 'Invalid data format'}), 400
        except Exception as e:
            db.session.rollback()
            error_logger.exception(f"Error saving manual grading adjustment: {str(e)}")
            return jsonify({'status': 'error', 'message': 'Internal server error'}), 500

    @app.route('/api/grading_rag/rebuild_index', methods=['POST'])
    @admin_required
    def rebuild_grading_rag_index():
        """Rebuild the FAISS index for grading RAG system (admin only)"""
        try:
            from grading_rag_system import GradingRAGSystem

            rag_system = GradingRAGSystem()

            # Create embeddings for all adjustments
            app_logger.info("Starting RAG index rebuild...")
            stats = rag_system.create_embeddings_for_all_adjustments(force_recreate=False)

            # Build FAISS index
            index_success = rag_system.build_faiss_index()

            if index_success:
                app_logger.info("RAG index rebuild completed successfully")
                return jsonify({
                    'status': 'success',
                    'message': 'RAG index rebuilt successfully',
                    'stats': stats
                })
            else:
                return jsonify({
                    'status': 'error',
                    'message': 'Failed to build FAISS index'
                }), 500

        except Exception as e:
            error_logger.exception(f"Error rebuilding grading RAG index: {str(e)}")
            return jsonify({'status': 'error', 'message': 'Internal server error'}), 500

    @app.route('/api/grading_rag/search_similar', methods=['POST'])
    @admin_required
    def search_similar_grading_adjustments():
        """Search for similar grading adjustments (admin only)"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'status': 'error', 'message': 'No data provided'}), 400

            marking_point_id = data.get('marking_point_id')
            student_answer = data.get('student_answer', '')
            top_k = data.get('top_k', 5)
            min_score = data.get('min_score', 0.3)

            if not marking_point_id:
                return jsonify({'status': 'error', 'message': 'marking_point_id is required'}), 400

            from grading_rag_system import GradingRAGSystem

            rag_system = GradingRAGSystem()

            # Build index if not already built
            if not rag_system.faiss_index:
                if not rag_system.build_faiss_index():
                    return jsonify({
                        'status': 'error',
                        'message': 'Could not build FAISS index'
                    }), 500

            # Search for similar adjustments
            results = rag_system.search_similar_for_marking_point(
                marking_point_id, student_answer, top_k, min_score
            )

            return jsonify({
                'status': 'success',
                'results': results,
                'count': len(results)
            })

        except Exception as e:
            error_logger.exception(f"Error searching similar grading adjustments: {str(e)}")
            return jsonify({'status': 'error', 'message': 'Internal server error'}), 500

    @app.route('/api/grading_rag/stats', methods=['GET'])
    @admin_required
    def get_grading_rag_stats():
        """Get statistics about the grading RAG system (admin only)"""
        try:
            # Get total adjustments
            total_adjustments = ManualGradingAdjustment.query.count()

            # Get total embeddings
            total_embeddings = GradingAdjustmentEmbedding.query.count()

            # Get embeddings by model
            from sqlalchemy import func
            embeddings_by_model = db.session.query(
                GradingAdjustmentEmbedding.model_name,
                func.count(GradingAdjustmentEmbedding.id)
            ).group_by(GradingAdjustmentEmbedding.model_name).all()

            # Get recent adjustments count (last 30 days)
            from datetime import datetime, timedelta
            thirty_days_ago = datetime.utcnow() - timedelta(days=30)
            recent_adjustments = ManualGradingAdjustment.query.filter(
                ManualGradingAdjustment.timestamp >= thirty_days_ago
            ).count()

            return jsonify({
                'status': 'success',
                'stats': {
                    'total_adjustments': total_adjustments,
                    'total_embeddings': total_embeddings,
                    'recent_adjustments_30d': recent_adjustments,
                    'embeddings_by_model': dict(embeddings_by_model),
                    'coverage_percentage': round((total_embeddings / total_adjustments * 100) if total_adjustments > 0 else 0, 2)
                }
            })

        except Exception as e:
            error_logger.exception(f"Error getting grading RAG stats: {str(e)}")
            return jsonify({'status': 'error', 'message': 'Internal server error'}), 500
