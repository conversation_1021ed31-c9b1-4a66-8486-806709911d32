#!/usr/bin/env python3
"""
Migration script to add grading_adjustment_embeddings table for RAG-based manual grading context.
This enables finding similar grading decisions across all marking points, not just the same marking point.
"""

import sqlite3
import os
import sys
from datetime import datetime

def get_db_path():
    """Get the database path from environment or use default."""
    return os.getenv('DATABASE_URL', 'instance/vast.db').replace('sqlite:///', '')

def run_migration():
    """Run the migration to add grading_adjustment_embeddings table."""
    db_path = get_db_path()
    
    if not os.path.exists(db_path):
        print(f"❌ Database not found at {db_path}")
        print("Please ensure the database exists before running this migration.")
        return False
    
    try:
        print(f"🔄 Connecting to database: {db_path}")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if table already exists
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='grading_adjustment_embeddings'
        """)
        
        if cursor.fetchone():
            print("⚠️  Table 'grading_adjustment_embeddings' already exists. Skipping migration.")
            conn.close()
            return True
        
        print("📝 Creating grading_adjustment_embeddings table...")
        
        # Create the grading_adjustment_embeddings table
        cursor.execute("""
            CREATE TABLE grading_adjustment_embeddings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                adjustment_id INTEGER NOT NULL,
                model_name VARCHAR(100) NOT NULL,
                embedding_vector TEXT NOT NULL,
                vector_dimension INTEGER NOT NULL,
                context_text TEXT NOT NULL,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (adjustment_id) REFERENCES manual_grading_adjustments (id)
            )
        """)
        
        # Create indexes for better performance
        cursor.execute("""
            CREATE INDEX idx_grading_embeddings_adjustment 
            ON grading_adjustment_embeddings (adjustment_id)
        """)
        
        cursor.execute("""
            CREATE INDEX idx_grading_embeddings_model 
            ON grading_adjustment_embeddings (model_name)
        """)
        
        cursor.execute("""
            CREATE INDEX idx_grading_embeddings_created 
            ON grading_adjustment_embeddings (created_at)
        """)
        
        # Commit the changes
        conn.commit()
        
        print("✅ Migration completed successfully!")
        print("📊 Table 'grading_adjustment_embeddings' created with indexes")
        
        # Verify the table was created
        cursor.execute("SELECT COUNT(*) FROM grading_adjustment_embeddings")
        count = cursor.fetchone()[0]
        print(f"📈 Table initialized with {count} records")
        
        conn.close()
        return True
        
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def rollback_migration():
    """Rollback the migration by dropping the grading_adjustment_embeddings table."""
    db_path = get_db_path()
    
    if not os.path.exists(db_path):
        print(f"❌ Database not found at {db_path}")
        return False
    
    try:
        print(f"🔄 Connecting to database: {db_path}")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if table exists
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='grading_adjustment_embeddings'
        """)
        
        if not cursor.fetchone():
            print("⚠️  Table 'grading_adjustment_embeddings' does not exist. Nothing to rollback.")
            conn.close()
            return True
        
        print("🗑️  Dropping grading_adjustment_embeddings table...")
        
        # Drop the table (indexes will be dropped automatically)
        cursor.execute("DROP TABLE grading_adjustment_embeddings")
        
        # Commit the changes
        conn.commit()
        
        print("✅ Rollback completed successfully!")
        print("📊 Table 'grading_adjustment_embeddings' has been dropped")
        
        conn.close()
        return True
        
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Manual Grading RAG Embeddings Migration")
    print("=" * 50)
    
    if len(sys.argv) > 1 and sys.argv[1] == "rollback":
        print("🔄 Running rollback...")
        success = rollback_migration()
    else:
        print("🔄 Running migration...")
        success = run_migration()
    
    if success:
        print("🎉 Operation completed successfully!")
    else:
        print("💥 Operation failed!")
        sys.exit(1)
