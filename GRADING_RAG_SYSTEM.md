# Manual Grading RAG System Implementation

## 🎯 Overview

The Manual Grading RAG (Retrieval-Augmented Generation) system enables the AI grading pipeline to learn from teacher feedback across **all marking points**, not just the same marking point. This creates a comprehensive knowledge base of grading decisions that improves AI grading consistency and accuracy over time.

## 🚀 Key Features

### 1. **Cross-Marking Point Learning**
- Finds similar grading decisions across different marking points
- Uses semantic similarity to match student answers and grading contexts
- Learns from teacher reasoning and adjustment patterns

### 2. **Intelligent Context Generation**
- Combines marking point descriptions, student answers, and teacher feedback
- Creates rich embeddings that capture grading nuances
- Provides contextual examples for AI grading decisions

### 3. **Real-time Integration**
- Automatically creates embeddings when teachers make manual adjustments
- Updates the knowledge base in real-time
- Provides immediate context for subsequent grading

### 4. **Semantic Search**
- Uses state-of-the-art `all-mpnet-base-v2` sentence transformer model
- FAISS vector database for fast similarity search
- Configurable similarity thresholds and result counts

## 📊 System Architecture

```
Manual Adjustment → Embedding Generation → FAISS Index → Context Retrieval → AI Grading
```

### Components

1. **GradingAdjustmentEmbedding Model**: Stores vector embeddings of manual adjustments
2. **GradingRAGSystem Class**: Core RAG functionality and search
3. **API Integration**: Automatic embedding creation and context retrieval
4. **FAISS Index**: Fast vector similarity search

## 🛠️ Implementation Details

### Database Schema

```sql
CREATE TABLE grading_adjustment_embeddings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    adjustment_id INTEGER NOT NULL,
    model_name VARCHAR(100) NOT NULL,
    embedding_vector TEXT NOT NULL,
    vector_dimension INTEGER NOT NULL,
    context_text TEXT NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (adjustment_id) REFERENCES manual_grading_adjustments (id)
);
```

### Context Text Format

Each embedding combines:
- **Marking Point**: Description and maximum score
- **Student Answer**: Truncated to 1000 characters
- **Grading Decision**: Original and adjusted scores
- **Teacher Reasoning**: Optional explanation
- **Subject/Topic Context**: For better categorization

Example:
```
Marking Point: Identify functional groups in organic compounds | Max Score: 2.0 | Student Answer: The molecule contains alcohol and ketone groups... | Original Score: 1.0 | Adjusted Score: 2.0 | Teacher Reason: Student correctly identified both groups | Topic: Organic Chemistry | Subject: Chemistry
```

### API Endpoints

#### 1. Rebuild RAG Index
```
POST /api/grading_rag/rebuild_index
```
- **Access**: Admin only
- **Function**: Recreates all embeddings and rebuilds FAISS index
- **Use Case**: System maintenance, model updates

#### 2. Search Similar Adjustments
```
POST /api/grading_rag/search_similar
```
- **Access**: Admin only
- **Parameters**: `marking_point_id`, `student_answer`, `top_k`, `min_score`
- **Function**: Find similar grading decisions for testing/analysis

#### 3. RAG Statistics
```
GET /api/grading_rag/stats
```
- **Access**: Admin only
- **Function**: Get system statistics and coverage metrics

## 🔧 Usage

### Automatic Integration

The system automatically integrates with the existing grading pipeline:

1. **When teachers make manual adjustments**: Embeddings are created in the background
2. **During AI grading**: RAG context is retrieved and included in prompts
3. **Fallback behavior**: Falls back to same-marking-point lookup if no similar adjustments found

### Manual Management

```python
# Initialize RAG system
from grading_rag_system import GradingRAGSystem
rag_system = GradingRAGSystem()

# Create embeddings for all adjustments
stats = rag_system.create_embeddings_for_all_adjustments()

# Build FAISS index
rag_system.build_faiss_index()

# Search for similar adjustments
results = rag_system.search_similar_for_marking_point(
    marking_point_id=123,
    student_answer="Student's answer text",
    top_k=3,
    min_score=0.4
)
```

## 📈 Performance Metrics

### Current Statistics
- **Model**: all-mpnet-base-v2 (768 dimensions)
- **Embedding Creation**: ~2-3 seconds per adjustment
- **Search Speed**: Sub-second for similarity search
- **Memory Usage**: ~50MB for 1000 adjustments

### Similarity Thresholds
- **0.8+**: Very high similarity (near-identical contexts)
- **0.6-0.8**: High similarity (similar concepts/answers)
- **0.4-0.6**: Medium similarity (related topics)
- **0.3-0.4**: Low similarity (minimal relevance)

## 🧪 Testing

Run the comprehensive test suite:

```bash
python test_grading_rag.py
```

Tests include:
- Embedding creation for existing adjustments
- FAISS index building and search
- Context generation for grading
- API endpoint functionality

## 🔄 Migration

The system includes a migration script:

```bash
python migrations/add_grading_adjustment_embeddings.py
```

For rollback:
```bash
python migrations/add_grading_adjustment_embeddings.py rollback
```

## 📝 Context Integration

The RAG system replaces the old `_get_manual_adjustment_context` function with semantic search:

### Before (Same Marking Point Only)
```python
def _get_manual_adjustment_context(marking_point_id, part_id, app_logger):
    # Only looked at same marking point
    recent_adjustments = ManualGradingAdjustment.query.filter(
        ManualGradingAdjustment.marking_point_id == marking_point_id
    ).limit(5).all()
```

### After (Cross-Marking Point RAG)
```python
def _get_manual_adjustment_context(marking_point_id, user_answer, app_logger):
    # Uses semantic search across all marking points
    context = get_rag_context_for_grading(
        marking_point_id=marking_point_id,
        student_answer=user_answer,
        top_k=3,
        min_score=0.4
    )
```

## 🎯 Benefits

1. **Improved Consistency**: AI learns from teacher patterns across all subjects
2. **Better Context**: Semantic similarity finds truly relevant examples
3. **Scalable Learning**: Knowledge base grows with every teacher adjustment
4. **Cross-Domain Transfer**: Insights from one topic help grade others
5. **Real-time Updates**: System improves continuously without manual intervention

## 🔮 Future Enhancements

1. **Subject-Specific Models**: Fine-tuned embeddings for different subjects
2. **Temporal Weighting**: More recent adjustments weighted higher
3. **Teacher Expertise**: Weight adjustments by teacher experience/accuracy
4. **Confidence Scoring**: Provide confidence levels for RAG suggestions
5. **Active Learning**: Identify cases where teacher input would be most valuable

## 🚨 Troubleshooting

### Common Issues

1. **No embeddings found**: Run `python grading_rag_system.py` to create initial embeddings
2. **FAISS index errors**: Ensure embeddings exist before building index
3. **Memory issues**: Reduce batch size for embedding creation
4. **Slow search**: Check FAISS index is built and loaded

### Monitoring

- Check `/api/grading_rag/stats` for system health
- Monitor embedding creation success rates
- Track context retrieval performance in grading logs

## 📚 Dependencies

- `sentence-transformers>=2.2.2`: For creating embeddings
- `faiss-cpu>=1.7.4`: For vector similarity search
- `scikit-learn>=1.3.0`: For vector normalization
- `numpy>=1.21.0`: For numerical operations
